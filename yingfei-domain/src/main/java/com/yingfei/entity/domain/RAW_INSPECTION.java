package com.yingfei.entity.domain;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.entity.dto.InspectionDataDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 原始检验数据表
 * @TableName raw_inspection
 */
@SuppressWarnings("AlibabaRemoveCommentedCode")
@TableName(value = "RAW_INSPECTION")
@Data
public class RAW_INSPECTION {

    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ID;

    /**
     * 抽样任务配置ID
     */
    private Long F_SAMPLING_TASK_CONFIG_ID;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    private String F_PLANT_NAME;

    /**
     * 产品名称
     */
    private String F_PART_NAME;

    /**
     * 过程名称
     */
    private String F_PRCS_NAME;

    /**
     * 产品版本名称
     */
    private String F_PTRV_NAME;

    /**
     * 批次名称
     */
    private String F_LOT_NAME;

    /**
     * 工单名称
     */
    private String F_JOB_NAME;

    /**
     * 工单组名称
     */
    private String F_JOB_GRP_NAME;

    /**
     * 班次名称
     */
    private String F_SHIFT_NAME;

    /**
     * 班次组名称
     */
    private String F_SHIFT_GRP_NAME;

    /**
     * 描述符信息
     */
    private String F_DESC_DATA;

    /**
     * 工艺流程名称
     */
    private String F_MFPS_NAME;

    /**
     * 工艺节点名称
     */
    private String F_MFND_NAME;

    /**
     * 检查计划名称
     */
    private String F_PLAN_NAME;

    @ApiModelProperty("子计划名称")
    private String F_CHILD_PLAN_NAME;

    /**
     * 测试数据，以JSON格式存储
     */
    private String F_TEST_DATA;

    /**
     * 推送时间
     */
    private Date F_PUSH_TIME;

    /**
     * 状态，0：未被抽样，1：已被抽样
     */
    private Integer  F_STATUS = 0;

    /**
     * 抽样时间
     */
    private Date F_SAMPLING_TIME;


//    public String buildMsg(String errorMsg) {
//        String testData = "";
//        if (StringUtils.isNotBlank(F_TEST_DATA)) {
//            final List<InspectionDataDTO.Test> tests = JSONArray.parseArray(F_TEST_DATA, InspectionDataDTO.Test.class);
//            for (InspectionDataDTO.Test test : tests) {
//                testData +=  I18nUtils.getMessage("TEST_NAME") + ":" +test.getTestName() + "\n\t";
//                for (InspectionDataDTO.TestVal testVal : test.getTestVal()) {
//                    testData +=  I18nUtils.getMessage("TEST_NO") + ":" +testVal.getNo() + "\n\t" +
//                            I18nUtils.getMessage("TEST_VAL") + ":" +testVal.getVal() + "\n\t\t";
//                    if(CollectionUtils.isNotEmpty(testVal.getSubTestData())){
//                        for (InspectionDataDTO.SubTest subTestDatum : testVal.getSubTestData()) {
//                            testData +=  I18nUtils.getMessage("SUB_TEST_VAL") + ":" +subTestDatum.getSubTestVal() + "\n";
//                        }
//                    }
//                }
//            }
//        }
//
//        return
//                I18nUtils.getMessage("F_SAMPLING_TASK_CONFIG_ID") + ":" + F_SAMPLING_TASK_CONFIG_ID + "\n" +
//                I18nUtils.getMessage("PLANT_NAME") + ":" + F_PLANT_NAME + "\n" +
//                I18nUtils.getMessage("MFPS_NAME") + ":" + F_MFPS_NAME + "\n" +
//                I18nUtils.getMessage("MFPS_NODE_NAME") + ":" + F_MFND_NAME + "\n" +
//                I18nUtils.getMessage("PLAN_NAME") + ":" + F_PLAN_NAME + "\n" +
//                I18nUtils.getMessage("CHILD_PLAN_NAME") + ":" + F_CHILD_PLAN_NAME + "\n" +
//                I18nUtils.getMessage("PART_NAME") + ":" + F_PART_NAME + "\n" +
//                I18nUtils.getMessage("PTRV_NAME") + ":" + F_PTRV_NAME + "\n" +
//                I18nUtils.getMessage("PRCS_NAME") + ":" + F_PRCS_NAME + "\n" +
//                I18nUtils.getMessage("TEST_DATA") + ":" + testData + "\n" +
//                I18nUtils.getMessage("LOT_NAME") + ":" + F_LOT_NAME + "\n" +
//                I18nUtils.getMessage("JOB_GRP_NAME") + ":" + F_JOB_GRP_NAME + "\n" +
//                I18nUtils.getMessage("JOB_NAME") + ":" + F_JOB_NAME + "\n" +
//                I18nUtils.getMessage("SHIFT_GRP_NAME") + ":" + F_SHIFT_GRP_NAME + "\n" +
//                I18nUtils.getMessage("SHIFT_NAME") + ":" + F_SHIFT_NAME + "\n" +
//                I18nUtils.getMessage("PUSH_TIME") + ":" + F_PUSH_TIME + "\n" +
//                I18nUtils.getMessage("ERROR_MSG") + ":" + errorMsg + "\n";
//    }

    /**
     * 组装通知消息
     * @param errorMsg
     * @return
     */
    public String buildMsg(String errorMsg) {
        StringBuilder messageBuilder = new StringBuilder();

        // 添加基本信息字段
        addInfoLine(messageBuilder, "F_SAMPLING_TASK_CONFIG_ID", F_SAMPLING_TASK_CONFIG_ID);
        addInfoLine(messageBuilder, "PLANT_NAME", F_PLANT_NAME);
        addInfoLine(messageBuilder, "MFPS_NAME", F_MFPS_NAME);
        addInfoLine(messageBuilder, "MFPS_NODE_NAME", F_MFND_NAME);
        addInfoLine(messageBuilder, "PLAN_NAME", F_PLAN_NAME);
        addInfoLine(messageBuilder, "CHILD_PLAN_NAME", F_CHILD_PLAN_NAME);
        addInfoLine(messageBuilder, "PART_NAME", F_PART_NAME);
        addInfoLine(messageBuilder, "PTRV_NAME", F_PTRV_NAME);
        addInfoLine(messageBuilder, "PRCS_NAME", F_PRCS_NAME);

        // 添加测试数据
        if (StringUtils.isNotBlank(F_TEST_DATA)) {
            final List<InspectionDataDTO.Test> tests = JSONArray.parseArray(F_TEST_DATA, InspectionDataDTO.Test.class);
            messageBuilder.append(I18nUtils.getMessage("TEST_DATA")).append(":</br>");

            for (InspectionDataDTO.Test test : tests) {
                messageBuilder.append("  ").append(I18nUtils.getMessage("TEST_NAME")).append(":")
                        .append(test.getTestName()).append("</br>");

                for (InspectionDataDTO.TestVal testVal : test.getTestVal()) {
                    messageBuilder.append("&nbsp;&nbsp;&nbsp;&nbsp;").append(I18nUtils.getMessage("TEST_NO")).append(":")
                            .append(testVal.getNo()).append("</br>");
                    messageBuilder.append("&nbsp;&nbsp;&nbsp;&nbsp;").append(I18nUtils.getMessage("TEST_VAL")).append(":")
                            .append(testVal.getVal()).append("</br>");

                    if(CollectionUtils.isNotEmpty(testVal.getSubTestData())) {
                        for (InspectionDataDTO.SubTest subTestDatum : testVal.getSubTestData()) {
                            messageBuilder.append("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;").append(I18nUtils.getMessage("SUB_TEST_VAL")).append(":")
                                    .append(subTestDatum.getSubTestVal()).append("</br>");
                        }
                    }
                }
            }
        }

        // 继续添加其他信息字段
        addInfoLine(messageBuilder, "LOT_NAME", F_LOT_NAME);
        addInfoLine(messageBuilder, "JOB_GRP_NAME", F_JOB_GRP_NAME);
        addInfoLine(messageBuilder, "JOB_NAME", F_JOB_NAME);
        addInfoLine(messageBuilder, "SHIFT_GRP_NAME", F_SHIFT_GRP_NAME);
        addInfoLine(messageBuilder, "SHIFT_NAME", F_SHIFT_NAME);
        addInfoLine(messageBuilder, "PUSH_TIME", F_PUSH_TIME);

        // 错误信息
        messageBuilder.append(I18nUtils.getMessage("ERROR_MSG")).append(":").append(errorMsg);

        return messageBuilder.toString();
    }

    /**
     * 辅助方法，用于添加信息行
     */
    private void addInfoLine(StringBuilder messageBuilder, String key, Object value) {
        messageBuilder.append(I18nUtils.getMessage(key)).append(":")
                .append(value != null ? value.toString() : "").append("</br>");
    }

}

