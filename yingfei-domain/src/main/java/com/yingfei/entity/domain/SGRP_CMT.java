package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 子组备注表
 * @TableName SGRP_CMT
 */
@TableName(value ="SGRP_CMT")
@Data
public class SGRP_CMT extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_CMT;

    /**
     * 子组ID
     */
    private Long F_SGRP;

    /**
     * 测试ID
     */
    private Long F_TEST;

    /**
     * 子组备注
     */
    private String F_NOTE;

    /**
     * 图片地址
     */
    private String F_IMG;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 创建用户
     */
    private Long F_CRUE;

    /**
     * 编辑用户
     */
    private Long F_EDUE;

    public static SGRP_CMT init() {
        SGRP_CMT data = new SGRP_CMT();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
