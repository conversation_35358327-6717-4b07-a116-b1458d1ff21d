package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 权限模板表
 * @TableName PERMISSION_TEMPLATE_INF
 */
@TableName(value ="PERMISSION_TEMPLATE_INF")
@Data
public class PERMISSION_TEMPLATE_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ID;

    /**
     * 模板名称
     */
    private String F_NAME;

    /**
     * 模板描述
     */
    private String F_DESC;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;


}
