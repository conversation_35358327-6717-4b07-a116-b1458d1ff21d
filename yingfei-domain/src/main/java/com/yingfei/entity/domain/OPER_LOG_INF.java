package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 操作日志表
 * @TableName OPER_LOG_INF
 */
@TableName(value ="OPER_LOG_INF")
@Data
public class OPER_LOG_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_OLOG;

    /**
     * 操作模块
     */
    private String F_TITLE;

    /**
     * 业务类型: 0=其它,1=新增,2=修改,3=删除,4=,5=导出,6=导入,7=强退,8= ,9=清空数据
     */
    private Integer F_TYPE = 0;

    /**
     * 请求方法
     */
    private String F_METHOD;

    /**
     * 请求方式
     */
    private String F_REQUEST;

    /**
     * 操作员
     */
    private String F_OPER_NAME;

    /**
     * 请求地址
     */
    private String F_OPER_URL;

    /**
     * 操作地址
     */
    private String F_OPER_IP;

    /**
     * 请求参数
     */
    private String F_OPER_PARAM;

    /**
     * 返回参数
     */
    private String F_RESULT;

    /**
     * 操作状态（0正常 1异常）
     */
    private Integer F_STATUS = 0;

    /**
     * 错误消息
     */
    private String F_EMSG;

    /**
     * 消耗时间毫秒
     */
    private Long F_COST_TIME;

    /**
     * 操作人主键
     */
    private Long F_OPER_ID;

    /**
     * 操作日志数据影响关联日志
     */
    private String F_DATA;

    public static OPER_LOG_INF init() {
        OPER_LOG_INF data = new OPER_LOG_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
