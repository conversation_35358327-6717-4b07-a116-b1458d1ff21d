package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * 多数据查询临时表
 *
 * @TableName TEMP_TABLE_INF
 */
@TableName(value = "TEMP_TABLE_INF")
@Data
public class TEMP_TABLE_INF {
    /**
     * 要查询的条件id
     */
    @TableId(type = IdType.NONE)
    private Long F_ID;

    /**
     * 同一次查询标识
     */
    private String F_IDENTIFY;

    /**
     * 临时记录id
     */
    @TableField(exist = false)
    private List<Long> idList;

    /**
     * 临时记录标识
     */
    private String uuid;
}
