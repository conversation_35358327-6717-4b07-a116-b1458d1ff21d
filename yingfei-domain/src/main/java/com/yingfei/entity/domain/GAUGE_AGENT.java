package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 量具 Agent和硬件标识对照表
 * @TableName GAUGE_AGENT
 */
@TableName(value ="GAUGE_AGENT")
@Data
public class GAUGE_AGENT extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_GAAG;

    /**
     * Agent名称
     */
    private String F_NAME;

    /**
     * 硬件标识
     */
    private String F_HARDWARE;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

}
