package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

import java.util.Date;

/**
 * 待删除记录表
 * @TableName DEL_LINK
 */
@TableName(value ="DEL_LINK")
@Data
public class DEL_LINK extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_DEL;

    /**
     * 基础信息主键
     */
    private Long F_RESOURCE;

    /**
     * 1=产品，2=过程，3=测试，后续有增加则递增
     */
    private Integer F_TYPE;

    /**
     * 删除截止时间
     */
    private Date F_EXPIRE_TIME;

    /**
     * 操作人
     */
    private Long F_CRUE;

    /**
     * 修改人
     */
    private Long F_EDUE;

    public static DEL_LINK init() {
        DEL_LINK data = new DEL_LINK();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
