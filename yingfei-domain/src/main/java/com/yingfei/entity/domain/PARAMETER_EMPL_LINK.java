package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName(value = "PARAMETER_EMPL_LINK")
@Data
public class PARAMETER_EMPL_LINK extends BaseEntity {

    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ID;

    /**
     * 类型(0:用户  1:角色)
     */
    private Integer F_TYPE;

    /**
     * 对应类型id
     */
    private Long F_DATA_ID;

    /**
     * 参数集id
     */
    private Long F_PARAMETER_ID;

    private Long F_CRUE;

    private Long F_EDUE;

}
