package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存检验类型信息表
 * @TableName INSPECTION_TYPE_DAT
 */
@TableName(value ="INSPECTION_TYPE_DAT")
@Data
public class INSPECTION_TYPE_DAT extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ID;

    /**
     * 检验类型所关联的检验类型组ID(INSPECTION_TYPE_GRP)
     */
    private Long F_INGP = 0L;

    /**
     * 检验类型名称
     */
    private String F_NAME;

    /**
     * 因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    public static INSPECTION_TYPE_DAT init() {
        INSPECTION_TYPE_DAT data = new INSPECTION_TYPE_DAT();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
