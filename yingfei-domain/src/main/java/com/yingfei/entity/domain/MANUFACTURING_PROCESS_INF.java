package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 流程图结构表
 * @TableName MANUFACTURING_PROCESS_INF
 */
@TableName(value ="MANUFACTURING_PROCESS_INF")
@Data
public class MANUFACTURING_PROCESS_INF extends BaseEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_MFPS;

    /**
     * 流程名称
     */
    private String F_NAME;

    /**
     * 流程图json对象(前端生成)
     */
    private String F_DATA;

    /**
     * 创建用户
     */
    private Long F_CRUE;

    /**
     * 编辑用户
     */
    private Long F_EDUE;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 工厂id
     */
    private Long F_PLNT = 0L;

    public static MANUFACTURING_PROCESS_INF init() {
        MANUFACTURING_PROCESS_INF data = new MANUFACTURING_PROCESS_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
