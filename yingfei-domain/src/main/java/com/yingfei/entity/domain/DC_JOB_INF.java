package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 自动采集任务配置
 *
 * @TableName DC_JOB_INF
 */
@TableName(value = "DC_JOB_INF")
@Data
public class DC_JOB_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_DCJB;

    /**
     * 名称
     */
    private String F_NAME;

    /**
     * 数据库地址
     */
    private String F_URL;

    /**
     * 数据库账号
     */
    private String F_USERNAME;

    /**
     * 数据库密码
     */
    private String F_PASSWORD;

    /**
     * 数据库配置表主键
     */
    private Long F_DBCO;

    /**
     * 任务开始时间
     */
    private Date F_START_TIME;

    /**
     * 时间间隔
     */
    private Integer F_TIME_INTERVAL;

    /**
     * 时间类型
     */
    private Integer F_TIME_TYPE;

    /**
     * 处理SQL
     */
    private String F_SQL;

    /**
     * 是否启用启动sql(0:否 1:是)
     */
    private Integer F_START_TYPE = 0;

    /**
     * 启动SQL
     */
    private String F_START_SQL;

    /**
     * 所选库名
     */
    private String F_DB_NAME;

    /**
     * 采集类型(1:数据库 2:文件)
     */
    private Integer F_TYPE = 1;

    /**
     * 文件连接配置json
     */
    private String F_FILE_CONFIG;

    /**
     * dcs配置json
     */
    private String F_DCS_CONFIG;

    /**
     * 记录创建用户ID
     */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

    public static DC_JOB_INF init() {
        DC_JOB_INF data = new DC_JOB_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
