package com.yingfei.dataManagement.service.bpm.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.bpm.enums.task.BpmProcessInstanceDeleteReasonEnum;
import com.yingfei.common.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.yingfei.common.bpm.enums.task.BpmProcessInstanceStatusEnum;
import com.yingfei.common.bpm.event.BpmProcessInstanceResultEventPublisher;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.mapper.BpmProcessInstanceExtMapper;
import com.yingfei.dataManagement.mapper.convert.BpmProcessInstanceConvert;
import com.yingfei.dataManagement.service.EVNT_INFService;
import com.yingfei.dataManagement.service.bpm.BpmProcessDefinitionService;
import com.yingfei.dataManagement.service.bpm.BpmProcessInstanceService;
import com.yingfei.dataManagement.service.bpm.BpmTaskAssignRuleService;
import com.yingfei.dataManagement.service.bpm.BpmTaskService;
import com.yingfei.entity.domain.BPM_PROCESS_INSTANCE;
import com.yingfei.entity.domain.BPM_TASK;
import com.yingfei.entity.domain.EVNT_INF;
import com.yingfei.entity.dto.AdminUserRespDTO;
import com.yingfei.entity.dto.BPM_PROCESS_DEFINITION_DTO;
import com.yingfei.entity.dto.BPM_PROCESS_INSTANCE_DTO;
import com.yingfei.entity.dto.CamundaProcessInstanceDTO;
import com.yingfei.entity.vo.BPM_PROCESS_INSTANCE_VO;
import com.yingfei.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.IdentityService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.Task;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;

import static com.yingfei.common.core.constant.WorkFlowConstants.*;
import static com.yingfei.common.core.utils.CollectionToMapUtils.convertList;


/**
 * 流程实例 Service 实现类
 * <p>
 * ProcessDefinition & ProcessInstance & Execution & Task 的关系：
 * 1. <a href="https://blog.csdn.net/bobozai86/article/details/105210414" />
 * <p>
 * HistoricProcessInstance & ProcessInstance 的关系：
 * 1. <a href=" https://my.oschina.net/843294669/blog/71902" />
 * <p>
 * 简单来说，前者 = 历史 + 运行中的流程实例，后者仅是运行中的流程实例
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class BpmProcessInstanceServiceImpl extends ServiceImpl<BpmProcessInstanceExtMapper, BPM_PROCESS_INSTANCE> implements BpmProcessInstanceService {

    @Resource
    private RuntimeService runtimeService;
    @Resource
    private BpmProcessInstanceExtMapper processInstanceExtMapper;
    @Resource
    @Lazy // 解决循环依赖
    private BpmTaskService bpmTaskService;
    @Resource
    private BpmProcessDefinitionService processDefinitionService;
    @Resource
    private HistoryService historyService;
    @Resource
    private RemoteUserService remoteUserService;
    @Resource
    private BpmProcessInstanceResultEventPublisher processInstanceResultEventPublisher;
    @Resource
    private IdentityService identityService;
    @Resource
    private BpmTaskAssignRuleService bpmTaskAssignRuleService;
    @Resource
    private EVNT_INFService evntInfService;

    @Override
    public ProcessInstance getProcessInstance(String id) {
        return runtimeService.createProcessInstanceQuery().processInstanceId(id).singleResult();
    }

    @Override
    public List<ProcessInstance> getProcessInstances(Set<String> ids) {
        return runtimeService.createProcessInstanceQuery().processInstanceIds(ids).matchVariableNamesIgnoreCase().list();
    }

    @Override
    public List<BPM_PROCESS_INSTANCE_DTO> getMyProcessInstancePage(BPM_PROCESS_INSTANCE_VO bpmProcessInstanceVo) {
        // 通过 BPM_PROCESS_INSTANCE_DTO 表，先查询到对应的分页
        bpmProcessInstanceVo.setF_START_USER(SecurityUtils.getUserId());
        List<BPM_PROCESS_INSTANCE_DTO> list = processInstanceExtMapper.getList(bpmProcessInstanceVo);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        // 获得流程 Task Map
        List<String> processInstanceIds = convertList(list, BPM_PROCESS_INSTANCE_DTO::getF_PROCESS_INSTANCE);
        Map<String, List<Task>> taskMap = bpmTaskService.getTaskMapByProcessInstanceIds(processInstanceIds);
        // 转换返回
        return BpmProcessInstanceConvert.INSTANCE.convertPage(list, taskMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createProcessInstance(BPM_PROCESS_INSTANCE_VO createReqVO) {
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getProcessDefinition(createReqVO.getF_PROCESS_DEFINITION());
        BPM_PROCESS_INSTANCE bpmProcessInstance = new BPM_PROCESS_INSTANCE();
        // 发起流程
        return createProcessInstance0(SecurityUtils.getUserId(), definition, createReqVO.getVariables(), createReqVO.getBusinessKey(), bpmProcessInstance);
    }

    @Override
    public String createAlarmProcessInstance(BPM_PROCESS_INSTANCE_VO createReqVO) {
        if (StringUtils.isEmpty(createReqVO.getF_EVNT())) {
            log.info("<---------------事件id不存在,请检查-------------->");
            return "";
        }
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getProcessDefinition(createReqVO.getF_PROCESS_DEFINITION());
        BPM_PROCESS_INSTANCE bpmProcessInstance = new BPM_PROCESS_INSTANCE();
        bpmProcessInstance.setF_EVNT(createReqVO.getF_EVNT());
        // 发起流程
        return createProcessInstance0(createReqVO.getF_CRUE() == null ? SecurityUtils.getUserId() : createReqVO.getF_CRUE(),
                definition, createReqVO.getVariables(), createReqVO.getBusinessKey(), bpmProcessInstance);
    }


    @Override
    public BPM_PROCESS_INSTANCE_DTO getProcessInstanceVO(String id) {
        // 获得流程实例
        HistoricProcessInstance processInstance = getHistoricProcessInstance(id);
        if (processInstance == null) {
            return null;
        }


        BPM_PROCESS_INSTANCE_DTO processInstanceExt = selectByProcessInstanceId(id);
        Assert.notNull(processInstanceExt, "流程实例拓展({}) 不存在", id);

        // 获得流程定义
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinition(processInstance.getProcessDefinitionId());
        Assert.notNull(processDefinition, "流程定义({}) 不存在", processInstance.getProcessDefinitionId());
        BPM_PROCESS_DEFINITION_DTO processDefinitionExt = processDefinitionService.getProcessDefinitionExt(
                processInstance.getProcessDefinitionId());
        Assert.notNull(processDefinitionExt, "流程定义拓展({}) 不存在", id);
        String bpmnXml = processDefinitionService.getProcessDefinitionBpmnXML(processInstance.getProcessDefinitionId());
        AdminUserRespDTO startUser = null;
        if(ObjectUtils.isNotEmpty(processInstance.getStartUserId())){
          // 获得 User
          R<AdminUserRespDTO> user = remoteUserService.getUser(Long.valueOf(processInstance.getStartUserId()));
          if (user.getData() != null) {
              startUser = user.getData();
          }
        }
//        DeptRespDTO dept = null;
//        if (startUser != null) {
//            dept = deptApi.getDept(startUser.getDeptId());
//        }

        // 拼接结果
        return BpmProcessInstanceConvert.INSTANCE.convert2(processInstance, processInstanceExt,
                processDefinition, processDefinitionExt, bpmnXml, startUser, null);
    }

    @Override
    public BPM_PROCESS_INSTANCE_DTO selectByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapper<BPM_PROCESS_INSTANCE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_PROCESS_INSTANCE::getF_PROCESS_INSTANCE, processInstanceId);
        BPM_PROCESS_INSTANCE bpmProcessInstance = processInstanceExtMapper.selectOne(queryWrapper);
        return BpmProcessInstanceConvert.INSTANCE.convert(bpmProcessInstance);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcessInstance(String processInstance) {
        HistoricProcessInstance instance = getHistoricProcessInstance(processInstance);
        if (instance == null) {
            /*ACT_表没有数据删除自建表记录*/
            /*删除对应流程实例和任务*/
            LambdaQueryWrapper<BPM_PROCESS_INSTANCE> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BPM_PROCESS_INSTANCE::getF_PROCESS_INSTANCE, processInstance);
            processInstanceExtMapper.delete(queryWrapper);

            LambdaQueryWrapper<BPM_TASK> BPM_TASKMapper = new LambdaQueryWrapper<>();
            BPM_TASKMapper.eq(BPM_TASK::getF_PROCESS_INSTANCE, processInstance);
            bpmTaskService.remove(BPM_TASKMapper);
//            throw new BusinessException(DataManagementExceptionEnum.PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS);
            return;
        }
        // 只能取消自己的
//        if (!Objects.equals(SecurityUtils.getUserId(), "1") && !Objects.equals(instance.getStartUserId(), SecurityUtils.getUserId())) {
//            throw new BusinessException(DataManagementExceptionEnum.PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF);
//        }
        this.updateProcessInstanceExtCancel(instance);
        // 通过删除流程实例，实现流程实例的取消,
        // 删除流程实例，正则执行任务 ACT_RU_TASK. 任务会被删除。通过历史表查询

        if (!instance.getState().equals("COMPLETED")) {
            /*删除进行中的流程实例务(删除运行中的流程实例时候，该实例会变成历史流程实例，所以需要两步删除)*/
            deleteProcessInstance(processInstance,
                    String.format(I18nUtils.getMessage(BpmProcessInstanceDeleteReasonEnum.CANCEL_TASK.toString()),I18nUtils.getMessage("DELETE")));
        }
        /*删除历史流程实例*/
        historyService.deleteHistoricProcessInstance(processInstance);
        /*删除对应流程实例和任务*/
        LambdaQueryWrapper<BPM_PROCESS_INSTANCE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_PROCESS_INSTANCE::getF_PROCESS_INSTANCE, processInstance);
        processInstanceExtMapper.delete(queryWrapper);

        LambdaQueryWrapper<BPM_TASK> BPM_TASKMapper = new LambdaQueryWrapper<>();
        BPM_TASKMapper.eq(BPM_TASK::getF_PROCESS_INSTANCE, processInstance);
        bpmTaskService.remove(BPM_TASKMapper);
    }

    @Override
    public long getTotal(BPM_PROCESS_INSTANCE_VO pageReqVO) {
        return processInstanceExtMapper.getTotal(pageReqVO);
    }

    @Override
    public void cancelProcessInstance(BPM_PROCESS_INSTANCE_VO cancelReqVO) {
        // 校验流程实例存在
        HistoricProcessInstance instance = getHistoricProcessInstance(cancelReqVO.getF_PROCESS_INSTANCE());
        if (instance == null) {
            throw new BusinessException(DataManagementExceptionEnum.PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS);
        }
        // 只能取消自己的
        if (!Objects.equals(SecurityUtils.getUserId(), "1") && !Objects.equals(instance.getStartUserId(), SecurityUtils.getUserId())) {
            throw new BusinessException(DataManagementExceptionEnum.PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF);
        }
        this.updateProcessInstanceExtCancel(instance);
        // 通过删除流程实例，实现流程实例的取消,
        // 删除流程实例，正则执行任务 ACT_RU_TASK. 任务会被删除。通过历史表查询
        deleteProcessInstance(cancelReqVO.getF_PROCESS_INSTANCE(),
                String.format(I18nUtils.getMessage(BpmProcessInstanceDeleteReasonEnum.CANCEL_TASK.toString()),cancelReqVO.getReason()));


    }

    /**
     * 获得历史的流程实例
     *
     * @param id 流程实例的编号
     * @return 历史的流程实例
     */
    @Override
    public HistoricProcessInstance getHistoricProcessInstance(String id) {
        return historyService.createHistoricProcessInstanceQuery().processInstanceId(id).singleResult();
    }

    @Override
    public List<HistoricProcessInstance> getHistoricProcessInstances(Set<String> ids) {
        return historyService.createHistoricProcessInstanceQuery().processInstanceIds(ids).list();
    }

    @Override
    public void createProcessInstanceExt(CamundaProcessInstanceDTO instance) {
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getProcessDefinition2(instance.getProcessDefinitionId());
        // 插入 BPM_PROCESS_INSTANCE_DTO 对象
        BPM_PROCESS_INSTANCE instanceExtDO = new BPM_PROCESS_INSTANCE()
                .setF_PROCESS_INSTANCE(instance.getProcessInstanceId())
                .setF_PROCESS_DEFINITION(definition.getId())
                .setF_NAME(instance.getProcessDefinitionName())
                .setF_START_USER(instance.getProcessStartUserId())
                .setF_CATEGORY(definition.getCategory())
                .setF_STATUS(BpmProcessInstanceStatusEnum.RUNNING.getStatus())
                .setF_RESULT(BpmProcessInstanceResultEnum.PROCESS.getResult())
                .setF_CRUE(instance.getF_CRUE())
                .setF_EDUE(instance.getF_EDUE());

        processInstanceExtMapper.insert(instanceExtDO);
    }

    @Override
    public void updateProcessInstanceExtCancel(HistoricProcessInstance historicProcessInstance) {

        // 更新拓展表
        BPM_PROCESS_INSTANCE instanceExtDO = new BPM_PROCESS_INSTANCE()
                .setF_PROCESS_INSTANCE(historicProcessInstance.getId())
                .setF_END_TIME(DateUtils.getNowDate()) // 由于 ProcessInstance 里没有办法拿到 endTime，所以这里设置
                .setF_STATUS(BpmProcessInstanceStatusEnum.FINISH.getStatus())
                .setF_RESULT(BpmProcessInstanceResultEnum.CANCEL.getResult());
        LambdaQueryWrapper<BPM_PROCESS_INSTANCE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_PROCESS_INSTANCE::getF_PROCESS_INSTANCE, historicProcessInstance.getId());
        processInstanceExtMapper.update(instanceExtDO, queryWrapper);

        // 发送流程实例的状态事件
        processInstanceResultEventPublisher.sendProcessInstanceResultEvent(
                BpmProcessInstanceConvert.INSTANCE.convert(this, historicProcessInstance, instanceExtDO.getF_RESULT()));
    }

    @Override
    public void updateProcessInstanceExtComplete(CamundaProcessInstanceDTO instance) {
        // 需要主动查询，因为 instance 只有 id 属性
        // 另外，此时如果去查询 ProcessInstance 的话，字段是不全的，所以去查询了 HistoricProcessInstance
        HistoricProcessInstance processInstance = getHistoricProcessInstance(instance.getProcessInstanceId());
        // 更新拓展表
        BPM_PROCESS_INSTANCE instanceExtDO = new BPM_PROCESS_INSTANCE()
                .setF_PROCESS_INSTANCE(instance.getProcessInstanceId())
                .setF_END_TIME(DateUtils.getNowDate()) // 由于 ProcessInstance 里没有办法拿到 endTime，所以这里设置
                .setF_STATUS(BpmProcessInstanceStatusEnum.FINISH.getStatus())
                .setF_RESULT(BpmProcessInstanceResultEnum.APPROVE.getResult()); // 如果正常完全，说明审批通过
        LambdaQueryWrapper<BPM_PROCESS_INSTANCE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_PROCESS_INSTANCE::getF_PROCESS_INSTANCE, processInstance.getId());

        processInstanceExtMapper.update(instanceExtDO, queryWrapper);

        // 发送流程被通过的消息
//        messageService.sendMessageWhenProcessInstanceApprove(BpmProcessInstanceConvert.INSTANCE.convert2ApprovedReq(instance));

        // 发送流程实例的状态事件
        processInstanceResultEventPublisher.sendProcessInstanceResultEvent(
                BpmProcessInstanceConvert.INSTANCE.convert(this, processInstance, instanceExtDO.getF_RESULT()));

        /*更新报警事件表状态为完成*/
        BPM_PROCESS_INSTANCE_DTO bpmProcessInstanceDto = selectByProcessInstanceId(instance.getProcessInstanceId());
        if (bpmProcessInstanceDto != null && StringUtils.isNotEmpty(bpmProcessInstanceDto.getF_EVNT())) {
            EVNT_INF evntInf = evntInfService.getById(bpmProcessInstanceDto.getF_EVNT());
            evntInf.setF_STATUS(YesOrNoEnum.YES.getType());
            evntInf.setF_EDTM(DateUtils.getNowDate());
            evntInfService.updateById(evntInf);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessInstanceExtReject(String id, String reason, boolean b) {
        // 需要主动查询，因为 instance 只有 id 属性
        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance(id);
        // 删除流程实例，取消整个审批流程
        if (b)
            deleteProcessInstance(id, StrUtil.format(I18nUtils.getMessage(BpmProcessInstanceDeleteReasonEnum.REJECT_TASK.toString()),reason));


        // 更新 status + result
        // 注意，不能和上面的逻辑更换位置。因为 deleteProcessInstance 会触发流程的取消，进而调用 updateProcessInstanceExtCancel 方法，
        // 设置 result 为 BpmProcessInstanceStatusEnum.CANCEL，显然和 result 不一定是一致的
        BPM_PROCESS_INSTANCE instanceExtDO = new BPM_PROCESS_INSTANCE()
                .setF_PROCESS_INSTANCE(id)
                .setF_STATUS(BpmProcessInstanceStatusEnum.FINISH.getStatus())
                .setF_RESULT(BpmProcessInstanceResultEnum.REJECT.getResult())
                .setF_END_TIME(DateUtils.getNowDate());
        LambdaQueryWrapper<BPM_PROCESS_INSTANCE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_PROCESS_INSTANCE::getF_PROCESS_INSTANCE, id);
        processInstanceExtMapper.update(instanceExtDO, queryWrapper);

        // 发送流程被不通过的消息
//        messageService.sendMessageWhenProcessInstanceReject(BpmProcessInstanceConvert.INSTANCE.convert2RejectReq(historicProcessInstance, reason));

        // 发送流程实例的状态事件
        processInstanceResultEventPublisher.sendProcessInstanceResultEvent(
                BpmProcessInstanceConvert.INSTANCE.convert(this, historicProcessInstance, instanceExtDO.getF_RESULT()));
    }

    private void deleteProcessInstance(String id, String reason) {
        runtimeService.deleteProcessInstance(id, reason);
    }

    private String createProcessInstance0(Long userId, ProcessDefinition definition,
                                          Map<String, Object> variables, String businessKey,
                                          BPM_PROCESS_INSTANCE bpmProcessInstance) {
        // 校验流程定义
        if (definition == null) {
            throw new BusinessException(DataManagementExceptionEnum.PROCESS_DEFINITION_NOT_EXISTS);
        }
        if (definition.isSuspended()) {
            throw new BusinessException(DataManagementExceptionEnum.PROCESS_DEFINITION_IS_SUSPENDED);
        }
        if (CollUtil.isEmpty(variables)) {
            variables = new HashMap<>();
        }
        variables.put(PROCESS_INSTANCE_NAME, definition.getName());
        variables.put(PROCESS_INSTANCE_STARTER_USER_ID, userId == null ? "1" : userId);
        variables.put(PROCESS_UNIQUE_IDENTIFICATION, UUID.randomUUID());
        // 创建流程实例
        identityService.setAuthenticatedUserId(String.valueOf(variables.get(PROCESS_INSTANCE_STARTER_USER_ID)));
        ProcessInstance instance = runtimeService.startProcessInstanceById(definition.getId(), businessKey, variables);

        LambdaQueryWrapper<BPM_PROCESS_INSTANCE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_PROCESS_INSTANCE::getF_PROCESS_INSTANCE, instance.getId());
        bpmProcessInstance.setF_PROCESS_INSTANCE(instance.getId());
        bpmProcessInstance.setF_FROM_VAL(JSONObject.toJSONString(variables));
        // 补全流程实例的拓展表
        processInstanceExtMapper.update(bpmProcessInstance, queryWrapper);
        return instance.getId();
    }

}
