package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 子组与描述符关联缓存表
 * @TableName SGRP_DSC_A
 */
@TableName(value ="SGRP_DSC_A")
@Data
public class SGRP_DSC_A {
    /**
     * 记录主键
     */
    @TableId(type = IdType.NONE)
    private Long F_SGRP;

    /**
     * 测试序列号
     */
    private Integer F_TSNO = 0;

    /**
     * 描述符组ID
     */
    private Long F_DSGP;

    /**
     * 描述符ID
     */
    private Long F_DESC;

    public static SGRP_DSC_A init() {
        SGRP_DSC_A data = new SGRP_DSC_A();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
