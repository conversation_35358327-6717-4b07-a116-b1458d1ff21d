package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.entity.vo.dataCollection.DataCollectionVO;
import lombok.Data;

import java.util.Date;


@Data
@TableName("SCHEDULE_JOB_INF")
public class SCHEDULE_JOB_INF extends BaseEntity {

    /**
     * 任务id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_SJOB;

    /**
     * 任务名称
     */
    private String F_NAME;

    /**
     * 任务标识
     */
    private String F_TASK_IDENTIFY;

    /**
     * cron表达式
     */
    private String F_CRON;

    /**
     * 任务状态 0：正常 1：暂停 2:删除
     */
    private Integer F_STATUS = 0;

    /**
     * 备注
     */
    private String F_REMARK;

    /**
     * 业务表id(按任务标识区分)
     */
    private Long F_BUID;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    /**
     * 业务条件json
     * @see DataCollectionVO
     */
    private String F_DATA;

    /**
     * 工厂名称
     */
    @TableField(exist = false)
    private String plntName;

    /**
     * 更新时是否启动任务0:否 1:是
     */
    @TableField(exist = false)
    private Integer startJob = 0;

    /**
     * 任务开始时间
     */
    @TableField(exist = false)
    private Date F_START_TIME;
    /**
     * 时间间隔
     */
    @TableField(exist = false)
    private Integer F_TIME_INTERVAL;
    /**
     * 时间类型
     * @see com.yingfei.entity.enums.TimeEnum
     */
    @TableField(exist = false)
    private Integer F_TIME_TYPE;



    public static SCHEDULE_JOB_INF init() {
        SCHEDULE_JOB_INF data = new SCHEDULE_JOB_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}


