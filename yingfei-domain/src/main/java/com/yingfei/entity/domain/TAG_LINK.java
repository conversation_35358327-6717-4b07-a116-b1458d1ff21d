package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 储存所有基础信息与Tag的关联关系
 * @TableName TAG_LINK
 */
@TableName(value ="TAG_LINK")
@Data
@Accessors(chain = true)
public class TAG_LINK extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_TAG_LINK;

    /**
     * 与TAG关联的基础信息主键
     */
    private Long F_RESOURCE;

    /**
     * 1=产品，2=过程，3=测试，后续有增加则递增
     */
    private Integer F_TYPE;

    /**
     * 被关联的标签ID
     */
    private Long F_TAG;

    /**
     * 被关联的标签对应的标签组ID
     */
    private Long F_TGGP;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;




}
