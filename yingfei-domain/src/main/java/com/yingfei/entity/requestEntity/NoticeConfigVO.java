package com.yingfei.entity.requestEntity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通知配置
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeConfigVO {

    /**
     * 1-历史数据导入，2-自动采集 3-第三方推送
     */
    Integer collectionType;

    /**
     *异常通知用户
     */
    private List<Long> noticeUser;

    /**
     * 异常通知角色
     */
    private List<Long> moticeRule;

    /**
     * 异常通知类型 notificationTypeEnum
     *  1:系统消息通知 2:邮件通知 3:企业微信通知 4:钉钉通知
     */
    private List<Integer> noticeType;




}
