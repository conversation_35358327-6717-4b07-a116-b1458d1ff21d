package com.yingfei.dataManagement.service.bpm;

import cn.hutool.db.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.utils.CollectionToMapUtils;
import com.yingfei.entity.domain.BPM_PROCESS_INSTANCE;
import com.yingfei.entity.dto.BPM_PROCESS_INSTANCE_DTO;
import com.yingfei.entity.dto.CamundaProcessInstanceDTO;
import com.yingfei.entity.vo.BPM_PROCESS_INSTANCE_VO;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.runtime.ProcessInstance;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 流程实例 Service 接口
 *
 * <AUTHOR>
 */
public interface BpmProcessInstanceService extends IService<BPM_PROCESS_INSTANCE> {

    /**
     * 获得流程实例
     *
     * @param id 流程实例的编号
     * @return 流程实例
     */
    ProcessInstance getProcessInstance(String id);

    /**
     * 获得流程实例列表
     *
     * @param ids 流程实例的编号集合
     * @return 流程实例列表
     */
    List<ProcessInstance> getProcessInstances(Set<String> ids);

    /**
     * 获得流程实例 Map
     *
     * @param ids 流程实例的编号集合
     * @return 流程实例列表 Map
     */
    default Map<String, ProcessInstance> getProcessInstanceMap(Set<String> ids) {
        return CollectionToMapUtils.convertMap(getProcessInstances(ids), ProcessInstance::getProcessInstanceId);
    }

    /**
     * 获得流程实例的分页
     *
     */
    List<BPM_PROCESS_INSTANCE_DTO> getMyProcessInstancePage(BPM_PROCESS_INSTANCE_VO bpmProcessInstanceVo);
    /**
     * 创建流程实例（提供给前端）
     *
     * @return 实例的编号
     */
    String createProcessInstance(BPM_PROCESS_INSTANCE_VO createReqVO);

    /**
     * 创建报警流程
     */
    String createAlarmProcessInstance(BPM_PROCESS_INSTANCE_VO createReqVO);

    /**
     * 获得流程实例 VO 信息
     *
     * @param id 流程实例的编号
     * @return 流程实例
     */
    BPM_PROCESS_INSTANCE_DTO getProcessInstanceVO(String id);

    /**
     * 取消流程实例
     */
    void cancelProcessInstance(BPM_PROCESS_INSTANCE_VO cancelReqVO);

    /**
     * 获得历史的流程实例
     *
     * @param id 流程实例的编号
     * @return 历史的流程实例
     */
    HistoricProcessInstance getHistoricProcessInstance(String id);

    /**
     * 获得历史的流程实例列表
     *
     * @param ids 流程实例的编号集合
     * @return 历史的流程实例列表
     */
    List<HistoricProcessInstance> getHistoricProcessInstances(Set<String> ids);

    /**
     * 获得历史的流程实例 Map
     *
     * @param ids 流程实例的编号集合
     * @return 历史的流程实例列表 Map
     */
    default Map<String, HistoricProcessInstance> getHistoricProcessInstanceMap(Set<String> ids) {
        return CollectionToMapUtils.convertMap(getHistoricProcessInstances(ids), HistoricProcessInstance::getId);
    }

    /**
     * 创建 ProcessInstance 拓展记录
     *
     * @param instance 流程任务
     */
    void createProcessInstanceExt(CamundaProcessInstanceDTO instance);

    /**
     * 更新 ProcessInstance 拓展记录为取消
     */
    void updateProcessInstanceExtCancel(HistoricProcessInstance processInstance);

    /**
     * 更新 ProcessInstance 拓展记录为完成
     *
     * @param instance 流程任务
     */
    void updateProcessInstanceExtComplete(CamundaProcessInstanceDTO instance);

    /**
     * 更新 ProcessInstance 拓展记录为不通过
     *
     * @param id 流程编号
     * @param reason 理由。例如说，审批不通过时，需要传递该值
     */
    void updateProcessInstanceExtReject(String id, String reason,boolean b);

    BPM_PROCESS_INSTANCE_DTO selectByProcessInstanceId(String processInstanceId);

    void deleteProcessInstance(String processInstance);

    long getTotal(BPM_PROCESS_INSTANCE_VO pageReqVO);
}
