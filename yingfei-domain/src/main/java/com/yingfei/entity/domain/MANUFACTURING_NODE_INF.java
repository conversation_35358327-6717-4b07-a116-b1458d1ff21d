package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 流程节点表
 * @TableName MANUFACTURING_NODE_INF
 */
@TableName(value ="MANUFACTURING_NODE_INF")
@Data
public class MANUFACTURING_NODE_INF extends BaseEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_MFND;

    /**
     * 节点名称
     */
    private String F_NAME;

    /**
     * 流程表主键
     */
    private Long F_MFPS;

    /**
     * 节点筛选条件
     */
    private String F_DATA;

    /**
     * 创建用户
     */
    private Long F_CRUE;

    /**
     * 编辑用户
     */
    private Long F_EDUE;

    /**
     * 检查计划数量
     */
    private Integer F_COUNT = 0;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    public static MANUFACTURING_NODE_INF init() {
        MANUFACTURING_NODE_INF data = new MANUFACTURING_NODE_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
