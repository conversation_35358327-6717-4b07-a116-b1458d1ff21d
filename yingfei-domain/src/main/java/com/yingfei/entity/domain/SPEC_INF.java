package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 公差限表
 * @TableName SPEC_INF
 */
@TableName(value ="SPEC_INF")
@Accessors(chain = true)
@Data
public class SPEC_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_SPEC;

    /**
     * 产品ID
     */
    private Long F_PART;

    /**
     * 测试ID
     */
    private Long F_TEST;

    /**
     * 过程ID
     */
    private Long F_PRCS;

    /**
     * 工作ID
     */
    private Long F_JOB;

    /**
     * 公差上限
     */
    private Double F_USL;

    /**
     * 目标值
     */
    private Double F_TAR;

    /**
     * 公差下限
     */
    private Double F_LSL;

    /**
     * 合理上限
     */
    private Double F_URL;

    /**
     * 合理下限
     */
    private Double F_LRL;

    /**
     * 报警上限
     */
    private Double F_UWL;

    /**
     * 报警下限
     */
    private Double F_LWL;

    /**
     * 件内上限
     */
    private Double F_UWP;

    /**
     * 件内下限
     */
    private Double F_LWP;

    /**
     * 子组均值上限
     */
    private Double F_UAL;

    /**
     * 子组均值下限
     */
    private Double F_LAL;

    /**
     * 目标Cp
     */
    private Double F_CP;

    /**
     * 目标Cpk
     */
    private Double F_CPK;

    /**
     * 目标Pp
     */
    private Double F_PP;

    /**
     * 目标Ppk
     */
    private Double F_PPK;

    /**
     * 各界限是否报警标识
     */
    private Integer F_AFLAG = 0;

    /**
     * 各界限是否激活标识
     */
    private Integer F_EFLAG = 0;

    /**
     * 因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 对应产品版本号主键
     */
    private Long F_PTRV;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录最后编辑用户ID
     */
    private Long F_EDUE;


    public static SPEC_INF init() {
        SPEC_INF data = new SPEC_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
    // 自定义验证方法
    public void initData() {
        if (F_PART == null) F_PART = 0L;
        if (F_PRCS == null) F_PRCS = 0L;
        if (F_TEST == null) F_TEST = 0L;
        if (F_PTRV == null) F_PTRV = 0L;
        if (F_USL == null) F_USL = 0d;
        if (F_TAR == null) F_TAR = 0d;
        if (F_LSL == null) F_LSL = 0d;
        if (F_URL == null) F_URL = 0d;
        if (F_LRL == null) F_LRL = 0d;
        if (F_UWL == null) F_UWL = 0d;
        if (F_LWL == null) F_LWL = 0d;
        if (F_UWP == null) F_UWP = 0d;
        if (F_LWP == null) F_LWP = 0d;
        if (F_UAL == null) F_UAL = 0d;
        if (F_LAL == null) F_LAL = 0d;
    }

}
