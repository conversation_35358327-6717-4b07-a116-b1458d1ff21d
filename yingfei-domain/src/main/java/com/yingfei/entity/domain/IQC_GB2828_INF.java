package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * @TableName IQC_GB2828_INF
 */

@Data
@Accessors(chain = true)
@TableName(value ="IQC_GB2828_INF")
public class IQC_GB2828_INF extends BaseEntity {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ID;

    /**
     * 检验等级
     */
    @Excel(name = "Inspection Level")
    private String F_LEVEL;

    /**
     * 检验类型
     */
    private String F_STATE;

    /**
     * AQL
     */
    private Double F_AQL;

    /**
     * 批次最大数量
     */
    private Integer F_LOT_MAX;

    /**
     * 批次最小数量
     */
    private Integer F_LOT_MIN;

    /**
     * Sample No
     */
    private Integer F_SAMPLE_NO;

    /**
     * Sample Size
     */
    private Integer F_SAMPLE_SIZE;

    /**
     * Accept
     */
    private Integer F_ACCEPT;

    /**
     * Reject
     */
    private Integer F_REJECT;

    /**
     * 是否一个组合的最后一行(0:否 1:是)
     */
    private Integer F_END = 0;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

}
