package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 过程信息表
 * @TableName PRCS_INF
 */
@TableName(value ="PRCS_INF")
@Data
public class PRCS_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_PRCS;

    /**
     * 工厂ID
     */
    private Long F_PLNT = 0L;

    /**
     * 上级过程ID，根过程的上级ID为0
     */
    private Long F_PARENT_PRCS = 0L;

    /**
     * 过程名称
     */
    private String F_NAME;

    /**
     * 过程详细名称
     */
    private String F_LONG_NAME = "";

    /**
     * 过程图片地址
     */
    private String F_IMAGE = "";

    /**
     * 过程因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    /**
     * 当前层级数
     */
    private Integer F_LEVEL_NUM = 1;

    public static PRCS_INF init() {
        PRCS_INF data = new PRCS_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
