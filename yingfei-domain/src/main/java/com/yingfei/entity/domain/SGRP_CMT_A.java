package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 子组备注缓存表
 * @TableName SGRP_CMT_A
 */
@TableName(value ="SGRP_CMT_A")
@Data
public class SGRP_CMT_A extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_CMT;

    /**
     * 子组ID
     */
    private Long F_SGRP;

    /**
     * 测试ID
     */
    private Long F_TEST;

    /**
     * 子组备注
     */
    private String F_NOTE;

    /**
     * 图片地址
     */
    private String F_IMG;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 创建用户
     */
    private Long F_CRUE;

    /**
     * 编辑用户
     */
    private Long F_EDUE;

    public static SGRP_CMT_A init(Long fcmt,Long fsgrp, Long ftest, String fnote) {
        SGRP_CMT_A data = new SGRP_CMT_A();
        data.setF_CMT(fcmt);
        data.setF_SGRP(fsgrp);
        data.setF_TEST(ftest);
        data.setF_NOTE(fnote);
        return data;
    }
    public static SGRP_CMT_A init() {
        SGRP_CMT_A data = new SGRP_CMT_A();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
