package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;


@Data
@TableName("SCHEDULE_JOB_LOG_INF")
public class SCHEDULE_JOB_LOG_INF extends BaseEntity {

    /**
     * 任务id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_JOBL;

    /**
     * 任务名称
     */
    private String F_NAME;

    /**
     * 任务id
     */
    private Long F_SJOB;

    /**
     * 任务执行时长
     */
    private Integer F_TIME;

    /**
     * 任务状态（0:成功,1:失败)
     */
    private Integer F_STATUS = 0;

    /**
     * 日志
     */
    private String F_LOG;


    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    public static SCHEDULE_JOB_LOG_INF init() {
        SCHEDULE_JOB_LOG_INF data = new SCHEDULE_JOB_LOG_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}


