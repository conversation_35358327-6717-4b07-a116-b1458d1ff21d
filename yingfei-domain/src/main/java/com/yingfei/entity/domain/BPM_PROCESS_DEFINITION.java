package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Bpm 流程定义的拓展表

 * @TableName BPM_PROCESS_DEFINITION
 */
@TableName(value ="BPM_PROCESS_DEFINITION")
@Accessors(chain = true)
@Data
public class BPM_PROCESS_DEFINITION extends BaseEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_BPDE;

    /**
     * 流程定义的编号
     */
    private String F_PROCESS_DEFINITION;

    /**
     * 流程模型的编号
     */
    private Long F_MODE;

    /**
     * 描述
     */
    private String F_DESCRIPTION;

    /**
     * 表单类型
     */
    private Integer F_FROM_TYPE = 20;

    /**
     * 表单表主键
     */
    private Long F_FROM;

    /**
     * 表单的配置
     */
    private String F_FROM_CONF;

    /**
     * 表单项的数组
     */
    private String F_FROM_FIELDS;

    /**
     * 自定义表单的提交路径
     */
    private String F_FROM_SUBMIT_PATH;

    /**
     * 自定义表单的查看路径
     */
    private String F_FROM_VIEW_PATH;

    /**
     * 是否删除(0:否 1:是)
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 创建用户
     */
    private Long F_CRUE;

    /**
     * 修改用户
     */
    private Long F_EDUE;

    public static BPM_PROCESS_DEFINITION init() {
        BPM_PROCESS_DEFINITION bpmProcessDefinition = new BPM_PROCESS_DEFINITION();
        BeanUtils.setAllFieldsToNull(bpmProcessDefinition);
        return bpmProcessDefinition;
    }
}
