package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 权限模板和菜单关联表
 * @TableName PERMISSION_MENU_LINK
 */
@TableName(value ="PERMISSION_MENU_LINK")
@Data
public class PERMISSION_MENU_LINK extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ID;

    /**
     * 权限模板表主键
     */
    private Long F_PERMISSION;

    /**
     * 菜单表主键
     */
    private Long F_MENU = 0L;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    public static PERMISSION_MENU_LINK init() {
        PERMISSION_MENU_LINK data = new PERMISSION_MENU_LINK();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
