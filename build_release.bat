@echo off
setlocal

rem 配置release目录
set RELEASE_DIR=%~dp0release

rem 清理release文件夹
if exist "%RELEASE_DIR%" (
    rd /s /q "%RELEASE_DIR%"
)
mkdir "%RELEASE_DIR%"

rem 编译项目
echo [INFO] 开始mvn clean install...
call mvn clean install
echo [DEBUG] Maven 执行完毕，继续执行脚本
rem 收集 JAR 文件到 release 文件夹
echo [INFO] 收集 jar 到 %RELEASE_DIR% 文件夹

copy /y "%~dp0yingfei-auth\target\yingfei-auth.jar" "%RELEASE_DIR%"
copy /y "%~dp0yingfei-modules\yingfei-modules-dataCollection\target\yingfei-modules-dataCollection.jar" "%RELEASE_DIR%"
copy /y "%~dp0yingfei-modules\yingfei-modules-dataManagement\target\yingfei-modules-dataManagement.jar" "%RELEASE_DIR%"
copy /y "%~dp0yingfei-modules\yingfei-modules-system\target\yingfei-modules-system.jar" "%RELEASE_DIR%"
copy /y "%~dp0yingfei-gateway\target\yingfei-gateway.jar" "%RELEASE_DIR%"
copy /y "%~dp0yingfei-modules\yingfei-modules-mq\target\yingfei-modules-mq.jar" "%RELEASE_DIR%"
copy /y "%~dp0yingfei-modules\yingfei-modules-report\target\yingfei-modules-report.jar" "%RELEASE_DIR%"

echo [INFO] 所有jar已收集到 %RELEASE_DIR%
pause
endlocal
