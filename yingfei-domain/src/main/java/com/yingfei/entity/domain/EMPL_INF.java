package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

import java.util.Date;

/**
 * 账户基本信息表
 *
 * @TableName EMPL_INF
 */
@TableName(value = "EMPL_INF")
@Data
public class EMPL_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_EMPL;

    /**
     * 员工工号
     */
    private String F_WORKID;

    /**
     * 所属部门ID
     */
    private Long F_HIER;

    /**
     * 员工名称
     */
    private String F_NAME;

    /**
     * 员工邮件
     */
    private String F_EMAIL;

    /**
     * 企业微信ID
     */
    private String F_WECHAT;

    /**
     * 钉钉ID
     */
    private String F_DINGDING;

    /**
     * 语言 1=简体中文  2=繁体中文 3=英文 4:泰文 其他语言暂不支持
     */
    private Integer F_LANG = 1;

    /**
     * 登录账户
     */
    private String F_CODE;

    /**
     * 登录密码
     */
    private String F_PSW;

    /**
     * 对应的角色ID
     */
    private Long F_ROLE;

    /**
     * 账户状态 1=激活 2=暂时失效 3=删除
     */
    private Integer F_STATUS = 1;

    /**
     * 账户过期时间，通常为临时账户设置此值。默认为2099-1-1
     */
    private Date F_EPTM;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;


    /**
     * 用户头像
     */
    private String F_IMAGE;

    /**
     * 检查计划看板状态(0:关闭,1:开启)
     */
    private Integer F_BOARD = 0;

    public static EMPL_INF init() {
        EMPL_INF data = new EMPL_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
