package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 账户角色表
 * @TableName ROLE_INF
 */
@TableName(value = "ROLE_INF")
@Data
public class ROLE_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ROLE;

    /**
     * 角色名称
     */
    private String F_NAME;

    /**
     * 部门主键
     */
    private Long F_HIER;

    /**
     * 是否超级管理员(0:否 1:是(一般不对外开放))
     */
    private Integer F_ADMIN = 0;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    /**
     * 权限模板表主键
     */
    private Long F_PERMISSION;

    public static ROLE_INF init() {
        ROLE_INF data = new ROLE_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
