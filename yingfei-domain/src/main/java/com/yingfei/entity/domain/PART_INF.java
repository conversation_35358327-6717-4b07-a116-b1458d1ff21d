package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 产品信息表
 * @TableName PART_INF
 */
@TableName(value ="PART_INF")
@Data
public class PART_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_PART;

    /**
     * 车间ID（产品归属于车间这一层级）
     */
    private Long F_PLNT = 0L;

    /**
     * 产品名称
     */
    private String F_NAME;

    /**
     * 产品详细名称
     */
    private String F_LONG_NAME = "";

    /**
     * 产品图片地址
     */
    private String F_IMAGE = "";


    /**
     * 因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;


    public static PART_INF init() {
        PART_INF data = new PART_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }

}
