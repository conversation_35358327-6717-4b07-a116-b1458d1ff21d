package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName(value = "EMPL_RESPONSIBLE_INF")
public class EMPL_RESPONSIBLE_INF extends BaseEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private Long F_RESP;

    /**
     * 业务类型 0=员工与测试映射
     */
    @ApiModelProperty(value = "业务类型 0=员工与测试映射")
    private Integer F_TYPE;

    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    private Long F_EMPL;

    /**
     * 映射业务JSON
     */
    @ApiModelProperty(value = "映射业务JSON 如果是F_TYPE=0，则为测试ID")
    private String F_DATA;

    /**
     * 是否删除标记，默认值为0
     */
    @ApiModelProperty(value = "是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    @ApiModelProperty(value = "记录创建用户ID")
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty(value = "记录编辑用户ID")
    private Long F_EDUE;


}
