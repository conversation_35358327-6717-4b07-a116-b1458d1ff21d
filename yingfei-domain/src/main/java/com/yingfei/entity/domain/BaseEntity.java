package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yingfei.entity.enums.DbLinkEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class BaseEntity {

    /**
     * 记录创建日期
     */
    @TableField(fill = FieldFill.INSERT)
    private Date F_CRTM;

    /**
     * 记录最后编辑日期
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date F_EDTM;

    /**
     * sql server 分页参数: 从第几条开始, 默认0
     */
    @TableField(exist = false)
    private Integer offset = 0;

    /**
     * sql server 分页参数: 每页多少条, 默认10
     */
    @TableField(exist = false)
    private Integer next = 10;

    @ApiModelProperty("创建人名称")
    @TableField(exist = false)
    private String createName;

    @ApiModelProperty("修改人名称")
    @TableField(exist = false)
    private String updateName;

    @TableField(exist = false)
    private Date startTime;

    @TableField(exist = false)
    private Date endTime;

    @TableField(exist = false)
    private Date edStartTime;

    @TableField(exist = false)
    private Date edEndTime;

    /**
     * 是否新增(0:否 1:是)
     */
    @TableField(exist = false)
    private Integer isAdd = 0;

    /**
     * 数据库类型
     * @see com.yingfei.entity.enums.DbLinkEnum
     */
    @TableField(exist = false)
    private Integer dbType = DbLinkEnum.SQL_SERVER.getType();



    public static <T> Page<T> convertToPage(Integer offset, Integer next) {
        // 参数校验
        if (offset == null) {
            offset = 0;
        }
        if (next == null) {
            next = 10;
        }
        if (offset < 0) {
            throw new IllegalArgumentException("offset 不能为负数");
        }
        if (next <= 0) {
            throw new IllegalArgumentException("next 必须为正数");
        }

        // 计算当前页码（向上取整）
        long current = offset == 0 ? 1 : (long) Math.ceil((double) offset / next);
        long size = next;

        return new Page<>(current, size);
    }
}
