package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 报警信息备注表
 * @TableName EVNT_COMMENT_INF
 */
@TableName(value ="EVNT_COMMENT_INF")
@Accessors(chain = true)
@Data
public class EVNT_COMMENT_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_EVCI;

    /**
     * 关联报警信息主键
     */
    private Long F_EVNT;

    /**
     * 备注
     */
    private String F_COMMENTS;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    /**
     * 类型(0:整体 1:原因  2:措施)
     */
    private Integer F_TYPE = 0;

    public static EVNT_COMMENT_INF init() {
        EVNT_COMMENT_INF data = new EVNT_COMMENT_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
