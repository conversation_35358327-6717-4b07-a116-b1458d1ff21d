package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分析页面模板表
 * @TableName ANALYSIS_DASHBOARD_TEMPLATE_INF
 */
@TableName(value ="ANALYSIS_DASHBOARD_TEMPLATE_INF")
@Data
public class ANALYSIS_DASHBOARD_TEMPLATE_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ADTI;

    /**
     * 模板名称
     */
    private String F_NAME;

    /**
     * 模板类型(0:聚合分析,1:单项分析)
     */
    private Integer F_TYPE;

    /**
     * 模板所选的图表及图表的配置json
     */
    private String F_DATA;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    /**
     * 描述
     */
    private String F_DESC;

    @ApiModelProperty("目标配置json")
    private String F_CONFIG;
}
