package com.yingfei.common.core.constant;


/**
 * 工作流程常量
 */
public interface WorkFlowConstants {

    String PROCESS_INSTANCE_NAME = "processInstanceName";
    String PROCESS_INSTANCE_STARTER_USER_ID = "startUserId";
    /*同一个流程的唯一标识*/
    String PROCESS_UNIQUE_IDENTIFICATION = "uniqueIdentification";
    String ALARMS = "Alarms";
    String ALARMS_DESCRIBE = "AlarmsDescribe";

    /**
     * 自定义代理人
     */
    String CUSTOM_AGENT = "custom_agent";

    Integer startTaskNum = 0;
    Integer endTaskNum = 1000;

    /**
     * 异常原因
     */
    String ROOT_CAUSE = "rootCause";

    /**
     * 改善措施
     */
    String RESPONSE_ACTION = "responseAction";

    /**
     * 当前操作用户
     */
    String userId = "userId";
}
