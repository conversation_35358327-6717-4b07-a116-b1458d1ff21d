package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;

import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 子组与描述符关联表
 *
 * @TableName SGRP_DSC
 */
@TableName(value = "SGRP_DSC")
@Data
public class SGRP_DSC {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_SGRP;

    /**
     * 测试序列号
     */
    private Integer F_TSNO = 0;

    /**
     * 描述符组ID
     */
    private Long F_DSGP;

    /**
     * 描述符ID
     */
    private Long F_DESC;

    /**
     * 描述符名称
     */
    @TableField(exist = false)
    private String descName;

    /**
     * 描述符组名称
     */
    @TableField(exist = false)
    private String descGrpName;

    public static SGRP_DSC init() {
        SGRP_DSC data = new SGRP_DSC();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
