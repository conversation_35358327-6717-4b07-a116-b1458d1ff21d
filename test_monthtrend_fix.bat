@echo off
echo ================================
echo MonthTrend接口JSON反序列化修复验证
echo ================================
echo.

echo 1. 清理并重新编译项目...
call mvn clean compile -q
if %errorlevel% neq 0 (
    echo 编译失败，请检查代码错误
    pause
    exit /b 1
)
echo 编译完成！

echo.
echo 2. 修复内容说明：
echo ================================
echo 问题：getMonthTrend接口报JSON反序列化错误
echo 原因：前端传递的emplIdList是字符串，后端期望List^<Long^>
echo.
echo 解决方案：
echo 1. 为MonthTrendQueryDTO的所有List^<Long^>字段添加自定义反序列化器
echo 2. 支持字符串格式："1,2,3"
echo 3. 支持数组格式：[1, 2, 3] 或 ["1", "2", "3"]
echo 4. 自动处理空值和空字符串
echo 5. 兼容多种前端传参格式
echo.

echo 3. 测试建议：
echo ================================
echo 请重新启动dataManagement服务，然后测试以下场景：
echo.
echo 场景1 - 字符串格式：
echo {
echo   "emplIdList": "1,2,3",
echo   "partIdList": "10,20,30",
echo   "startDate": "2024-01",
echo   "endDate": "2024-08"
echo }
echo.
echo 场景2 - 数组格式：
echo {
echo   "emplIdList": [1, 2, 3],
echo   "partIdList": [10, 20, 30],
echo   "startDate": "2024-01",
echo   "endDate": "2024-08"
echo }
echo.
echo 场景3 - 字符串数组格式：
echo {
echo   "emplIdList": ["1", "2", "3"],
echo   "partIdList": ["10", "20", "30"],
echo   "startDate": "2024-01",
echo   "endDate": "2024-08"
echo }
echo.

echo 4. 接口地址：
echo POST /monthTrend/getMonthTrend
echo.

echo 修复完成！请重启服务进行测试。
pause
