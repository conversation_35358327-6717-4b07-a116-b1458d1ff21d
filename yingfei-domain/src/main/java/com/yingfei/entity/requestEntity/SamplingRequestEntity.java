package com.yingfei.entity.requestEntity;

import com.yingfei.entity.domain.RAW_INSPECTION;
import com.yingfei.entity.domain.SAMPLING_TASK_CONFIG;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 抽样任务BO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SamplingRequestEntity {
    /**
     * 抽样任务配置
     */
    @ApiModelProperty("抽样任务配置")
    private SAMPLING_TASK_CONFIG config;
    /**
     * 原始检测数据
     */
    @ApiModelProperty("原始检测数据")
    private RAW_INSPECTION rawInspection;
}
