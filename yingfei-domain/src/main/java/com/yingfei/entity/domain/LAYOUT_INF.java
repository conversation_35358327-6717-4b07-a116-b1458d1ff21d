package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 用户菜单列配置
 * @TableName LAYOUT_INF
 */
@TableName(value ="LAYOUT_INF")
@Data
public class LAYOUT_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ID;

    /**
     * 保存用户id (如果是全局配置  该id默认是0)
     */
    private Long F_EMPL;

    /**
     * 菜单ID
     */
    private Long F_MENU;

    /**
     * 列保存信息
     */
    private String F_DATA;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    /**
     * 1=系统页面布局；2=看板风格；3=模型名称
     */
    private Integer F_TYPE = 1;

    public static LAYOUT_INF init() {
        LAYOUT_INF data = new LAYOUT_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
