@echo off
echo ================================
echo Jackson时间格式修复验证脚本
echo ================================
echo.

echo 1. 清理并重新编译项目...
call mvn clean compile -q
if %errorlevel% neq 0 (
    echo 编译失败，请检查代码错误
    pause
    exit /b 1
)
echo 编译完成！

echo.
echo 2. 启动系统服务进行测试...
echo 请手动启动yingfei-system服务，然后测试以下接口：
echo.
echo ================================
echo 测试接口列表：
echo ================================
echo.
echo 1. Jackson配置测试接口：
echo GET http://localhost:9210/jackson-test/config-info
echo GET http://localhost:9210/jackson-test/date-format
echo.
echo 2. 业务接口测试：
echo POST http://localhost:9210/oper_log_inf/list
echo Content-Type: application/json
echo.
echo 请求体：
echo {
echo   "offset": 0,
echo   "next": 10
echo }
echo.
echo ================================
echo 预期返回格式：
echo ================================
echo.
echo 时间字段应为字符串格式：
echo {
echo   "F_CRTM": "2024-01-15T10:30:45.123Z",
echo   "F_EDTM": "2024-01-15T10:30:45.123Z"
echo }
echo.
echo 如果时间字段显示为数字（如1705312245123），说明修复未生效
echo 如果时间字段显示为字符串格式，说明修复成功
echo.
echo ================================
echo 故障排除步骤：
echo ================================
echo.
echo 1. 检查控制台是否输出Jackson配置调试信息
echo 2. 访问 /jackson-test/config-info 查看ObjectMapper配置
echo 3. 确认WRITE_DATES_AS_TIMESTAMPS为false
echo 4. 检查是否有FastJSON冲突
echo.
echo ================================
echo 修复内容总结：
echo ================================
echo 1. 移除了pom.xml中的jackson.version配置
echo 2. 创建了专门的JacksonConfig全局配置类
echo 3. 修改了WebMvcConfig，确保Jackson优先级
echo 4. 添加了FastJSON冲突排除配置
echo 5. 通过全局配置处理所有Date类型，无需在实体类中添加注解
echo 6. 统一使用UTC时区和ISO 8601格式
echo.
echo ================================
echo 优势：
echo ================================
echo - 避免在每个实体类中使用@JsonFormat注解
echo - 配置集中管理，易于维护
echo - 全局生效，确保所有时间字段格式一致
echo - 解决了FastJSON2冲突问题
echo ================================
pause
