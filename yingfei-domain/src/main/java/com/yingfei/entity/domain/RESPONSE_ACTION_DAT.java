package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存改善措施信息表
 * @TableName RESPONSE_ACTION_DAT
 */
@TableName(value ="RESPONSE_ACTION_DAT")
@Data
public class RESPONSE_ACTION_DAT extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_RSAT;

    /**
     * 改善措施所关联的改善措施组ID
     */
    private Long F_RAGP = 0L;

    /**
     * 改善措施名称
     */
    private String F_NAME;

    /**
     * 改善措施因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;


    public static RESPONSE_ACTION_DAT init() {
        RESPONSE_ACTION_DAT data = new RESPONSE_ACTION_DAT();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }

}
