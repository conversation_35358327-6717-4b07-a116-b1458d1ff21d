package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 测试信息表
 * @TableName TEST_INF
 */
@TableName(value ="TEST_INF")
@Data
public class TEST_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_TEST;

    /**
     * 工厂id
     */
    private Long F_PLNT = 0L;

    /**
     * 测试名称
     */
    private String F_NAME;

    /**
     * 测试详细名称
     */
    private String F_LONG_NAME = "";

    /**
     * 测试图片地址
     */
    private String F_IMAGE = "";

    /**
     * 1=变量；2=缺陷；3=不良；4=检查表
     */
    private Integer F_TYPE = 1;

    /**
     * 关联的缺陷组主键(DEF_GRP)
     */
    private Long F_DFGP = 0L;

    /**
     * 测试单位主键(UNIT_INF)
     */
    private Long F_UNIT = 0L;

    /**
     * 测试因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    /**
     * 特征类别(0:一般特性 1:关键特性 2:重要特性 3:特殊特性)
     */
    private Integer F_LEVEL = 0;

    public static TEST_INF init() {
        TEST_INF data = new TEST_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
