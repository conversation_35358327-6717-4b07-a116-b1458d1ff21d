package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 枚举翻译字典表
 * @TableName DICT_INF
 */
@TableName(value ="DICT_INF")
@Data
public class DICT_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_DICT;

    /**
     * 分组描述
     */
    private String F_GROUP;

    /**
     * 字典唯一标识
     */
    private String F_CODE;

    /**
     * 语言类型(1:简体中文 2:繁体中文 3:英文 4:泰文)
     */
    private Integer F_TYPE;

    /**
     * 对应语言描述
     */
    private String F_CONTENT;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    public static DICT_INF init() {
        DICT_INF data = new DICT_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
