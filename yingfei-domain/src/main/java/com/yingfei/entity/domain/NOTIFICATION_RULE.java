package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 报警通知配置表
 *
 * @TableName NOTIFICATION_RULE
 */
@TableName(value = "NOTIFICATION_RULE")
@Data
public class NOTIFICATION_RULE extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_NOTI;

    /**
     * 报警通知规则名称
     */
    private String F_NAME;

    /**
     * 报警筛选范围json
     * @see com.yingfei.entity.dto.NotificationDataJsonDTO
     */
    private String F_DATA;

    /**
     * 报警类型(1:公差限 2:控制限)
     */
    private Integer F_ALR_TYPE = 1;

    /**
     * 具体报警(见对应的公差限枚举和控制限枚举
     * @see com.yingfei.entity.enums.SpecificationLimitViolation
     * @see com.yingfei.entity.domain.RULE_INF F_RULE_TYPE字段
     * )
     */
    private String F_ALR_DETAIL;

    /**
     * 收件人(多个逗号分割)
     */
    private String F_EMPL;

    /**
     * 通知类型,可多选(1:系统消息通知 2:邮件通知 3:企业微信通知 4:钉钉通知 5:其他通知)
     */
    private String F_NOTI_TYPE = "1";

    /**
     * 处理方式(
     * 0:不做处理
     * 1:填写异常原因
     * 2:填写改善措施
     * 3:填写异常原因和改善措施
     * )
     */
    private Integer F_STATUS = 0;

    /**
     * 流程定义的编号(为空不走流程)
     */
    private String F_PROCESS_DEFINITION;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    /**
     * 异常原因所选条件
     * @see com.yingfei.entity.dto.NotificationCauseDataDTO
     */
    private String F_CAUSE_DATA;

    /**
     * 改善措施所选条件
     * @see com.yingfei.entity.dto.NotificationCauseDataDTO
     */
    private String F_ACTION_DATA;

    /**
     * 0:用户 1:角色
     */
    private Integer F_EMPL_TYPE;

    /**
     * 描述
     */
    private String F_DESC;

    /**
     * 工厂id
     */
    private Long F_PLNT = 0L;

    public static NOTIFICATION_RULE init() {
        NOTIFICATION_RULE data = new NOTIFICATION_RULE();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
