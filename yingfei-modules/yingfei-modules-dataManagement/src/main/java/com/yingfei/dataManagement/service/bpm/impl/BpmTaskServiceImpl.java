package com.yingfei.dataManagement.service.bpm.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.bpm.enums.task.BpmProcessInstanceDeleteReasonEnum;
import com.yingfei.common.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.constant.WorkFlowConstants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.dataManagement.config.InitConfig;
import com.yingfei.dataManagement.mapper.BPM_TASKMapper;
import com.yingfei.dataManagement.mapper.convert.BpmTaskConvert;
import com.yingfei.dataManagement.service.EVNT_INFService;
import com.yingfei.dataManagement.service.bpm.*;
import com.yingfei.entity.domain.BPM_PROCESS_DEFINITION;
import com.yingfei.entity.domain.BPM_PROCESS_INSTANCE;
import com.yingfei.entity.domain.BPM_TASK;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.vo.*;
import com.yingfei.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.history.HistoricTaskInstanceQuery;
import org.camunda.bpm.engine.runtime.ActivityInstance;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.engine.task.TaskQuery;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.xml.instance.DomElement;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.yingfei.common.core.utils.CollectionToMapUtils.convertMap;
import static com.yingfei.common.core.utils.CollectionToMapUtils.convertSet;

/**
 * 流程任务实例 Service 实现类
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Slf4j
@Service
public class BpmTaskServiceImpl extends ServiceImpl<BPM_TASKMapper, BPM_TASK> implements BpmTaskService {

    @Resource
    private TaskService taskService;
    @Resource
    private HistoryService historyService;
    @Resource
    private BpmProcessInstanceService processInstanceService;
    @Resource
    private RemoteUserService remoteUserService;
    @Resource
    private BPM_TASKMapper bpmTaskMapper;
    @Resource
    private BpmTaskAssignRuleService bpmTaskAssignRuleService;
    @Resource
    private BpmProcessDefinitionService bpmProcessDefinitionService;
    @Resource
    private BpmFormService bpmFormService;
    @Resource
    private RuntimeService runtimeService;
    @Resource
    private EVNT_INFService evntInfService;

    @Override
    public TableDataInfo<BpmTaskTodoPageItemRespVO> getTodoTaskPage(Long userId, BpmTaskTodoPageReqVO pageVO) {
        // 查询待办任务
        TaskQuery taskQuery = taskService.createTaskQuery().taskAssigneeLike("%" + userId + "%") // 分配给自己
                .orderByTaskCreateTime().desc(); // 创建时间倒序
        if (StrUtil.isNotBlank(pageVO.getName())) {
            taskQuery.taskNameLike("%" + pageVO.getName() + "%");
        }
        if (StrUtil.isNotBlank(pageVO.getTaskNum())) {
            taskQuery.taskId(pageVO.getTaskNum());
        }
        if (pageVO.getStartTime() != null) {
            taskQuery.taskCreatedAfter(pageVO.getStartTime());
        }
        if (pageVO.getEndTime() != null) {
            taskQuery.taskCreatedBefore(pageVO.getEndTime());
        }
        // 执行查询
        List<Task> tasks = taskQuery.listPage(pageVO.getOffset(), pageVO.getNext());
        TableDataInfo<BpmTaskTodoPageItemRespVO> tableDataInfo = new TableDataInfo<>();
        if (CollUtil.isEmpty(tasks)) {
            return tableDataInfo;
        }

        // 获得 ProcessInstance Map
        // 获得 ProcessInstance Map
        Map<String, HistoricProcessInstance> historicProcessInstanceMap =
                processInstanceService.getHistoricProcessInstanceMap(
                        convertSet(tasks, Task::getProcessInstanceId));
        // 获得 User Map
        EMPL_INF_VO emplInfVo = new EMPL_INF_VO();
        emplInfVo.setIds(new ArrayList<>(convertSet(historicProcessInstanceMap.values(), HistoricProcessInstance::getStartUserId)).stream()
                .filter(StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .collect(Collectors.toList()));
        R<Map<Long, AdminUserRespDTO>> r = remoteUserService.getUserMap(emplInfVo);
        Map<Long, AdminUserRespDTO> userMap = new HashMap<>();
        if (r.getData() != null)
            userMap = r.getData();
        tableDataInfo.setTotal(taskQuery.count());
        List<BpmTaskTodoPageItemRespVO> bpmTaskTodoPageItemRespVOS = BpmTaskConvert.INSTANCE.convertList1(tasks, historicProcessInstanceMap, userMap);
        bpmTaskTodoPageItemRespVOS.forEach(bpmTaskTodoPageItemRespVO -> {
            if (bpmTaskTodoPageItemRespVO.getProcessInstance() == null) return;
            BPM_PROCESS_INSTANCE_DTO bpmProcessInstanceDto =
                    processInstanceService.selectByProcessInstanceId(bpmTaskTodoPageItemRespVO.getProcessInstance().getId());
            if (bpmProcessInstanceDto == null || StringUtils.isEmpty(bpmProcessInstanceDto.getF_EVNT())) return;
            EVNT_INF_DTO evntInfDto = evntInfService.getInfo(bpmProcessInstanceDto.getF_EVNT());
            bpmTaskTodoPageItemRespVO.setEvntInfDto(evntInfDto);
        });
        tableDataInfo.setRows(bpmTaskTodoPageItemRespVOS);
        // 拼接结果
        return tableDataInfo;
    }

    @Override
    public TableDataInfo<BpmTaskDonePageItemRespVO> getDoneTaskPage(Long userId, BpmTaskTodoPageReqVO pageVO) {
        // 查询已办任务
        HistoricTaskInstanceQuery taskQuery = historyService.createHistoricTaskInstanceQuery().finished() // 已完成
                .taskAssigneeLike("%" + userId + "%") // 分配给自己
                .orderByHistoricTaskInstanceEndTime().desc(); // 审批时间倒序
        if (StrUtil.isNotBlank(pageVO.getName())) {
            taskQuery.taskNameLike("%" + pageVO.getName() + "%");
        }
        if (StrUtil.isNotBlank(pageVO.getTaskNum())) {
            taskQuery.taskId(pageVO.getTaskNum());
        }
        if (pageVO.getStartTime() != null) {
            taskQuery.finishedAfter(pageVO.getStartTime());
        }
        if (pageVO.getEndTime() != null) {
            taskQuery.finishedBefore(pageVO.getEndTime());
        }
        // 执行查询
        List<HistoricTaskInstance> tasks = taskQuery.listPage(pageVO.getOffset(), pageVO.getNext());
        TableDataInfo<BpmTaskDonePageItemRespVO> tableDataInfo = new TableDataInfo<>();
        if (CollUtil.isEmpty(tasks)) {
            return tableDataInfo;
        }

        // 获得 TaskExtDO Map
        List<BPM_TASK_DTO> bpmTaskExtDOs = selectListByTaskIds(convertSet(tasks, HistoricTaskInstance::getId));
        Map<String, BPM_TASK_DTO> bpmTaskExtDOMap = convertMap(bpmTaskExtDOs, BPM_TASK_DTO::getF_TASK_NUM);
        // 获得 ProcessInstance Map
        Map<String, HistoricProcessInstance> historicProcessInstanceMap =
                processInstanceService.getHistoricProcessInstanceMap(
                        convertSet(tasks, HistoricTaskInstance::getProcessInstanceId));

        EMPL_INF_VO emplInfVo = new EMPL_INF_VO();
        emplInfVo.setIds(new ArrayList<>(convertSet(historicProcessInstanceMap.values(), HistoricProcessInstance::getStartUserId))
                .stream().filter(StringUtils::isNotEmpty).map(Long::valueOf).collect(Collectors.toList()));
        R<Map<Long, AdminUserRespDTO>> r = remoteUserService.getUserMap(emplInfVo);
        // 获得 User Map
        Map<Long, AdminUserRespDTO> userMap = new HashMap<>();
        if (r.getData() != null)
            userMap = r.getData();

        tableDataInfo.setTotal(taskQuery.count());
        List<BpmTaskDonePageItemRespVO> bpmTaskDonePageItemRespVOS =
                BpmTaskConvert.INSTANCE.convertList2(tasks, bpmTaskExtDOMap, historicProcessInstanceMap, userMap);

        bpmTaskDonePageItemRespVOS.forEach(bpmTaskDonePageItemRespVO -> {
            BPM_PROCESS_INSTANCE_DTO bpmProcessInstanceDto =
                    processInstanceService.selectByProcessInstanceId(bpmTaskDonePageItemRespVO.getProcessInstance().getId());
            if (bpmProcessInstanceDto == null || StringUtils.isEmpty(bpmProcessInstanceDto.getF_EVNT())) return;
            EVNT_INF_DTO evntInfDto = evntInfService.getInfo(bpmProcessInstanceDto.getF_EVNT());
            bpmTaskDonePageItemRespVO.setEvntInfDto(evntInfDto);
        });

        tableDataInfo.setRows(bpmTaskDonePageItemRespVOS);
        // 拼接结果
        return tableDataInfo;
    }

    @Override
    public List<Task> getTasksByProcessInstanceIds(List<String> processInstanceIds) {
        if (CollUtil.isEmpty(processInstanceIds)) {
            return Collections.emptyList();
        }
        return taskService.createTaskQuery().processInstanceIdIn(processInstanceIds.toArray(new String[0])).list();
    }

    @Override
    public List<BpmTaskRespVO> getTaskListByProcessInstanceId(String processInstanceId) {
        // 获得任务列表
        List<HistoricTaskInstance> tasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByHistoricActivityInstanceStartTime().desc()
                .list();
        BpmTaskInfoVO bpmTaskInfoVO = new BpmTaskInfoVO();
        if (CollUtil.isEmpty(tasks)) return new ArrayList<>();

        /*获取对应的报警信息*/
        BPM_PROCESS_INSTANCE_DTO bpmProcessInstanceDto = processInstanceService.selectByProcessInstanceId(processInstanceId);
        if (bpmProcessInstanceDto == null) return new ArrayList<>();
        EVNT_INF_DTO evntInfDto = evntInfService.getInfo(bpmProcessInstanceDto.getF_EVNT());

        /*获取最近一个月产品过程测试触发的其他报警*/
        List<EVNT_INF_DTO> causeEvntInfDtoList = new ArrayList<>();
        List<EVNT_INF_DTO> measuresEvntInfDtoList = new ArrayList<>();
        if (evntInfDto != null) {
            Date nowDate = DateUtils.getNowDate();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(nowDate);
            calendar.add(Calendar.MONTH, -1);
            EVNT_INF_VO evntInfVo = new EVNT_INF_VO();
            evntInfVo.setF_PART(evntInfDto.getF_PART())
                    .setF_PRCS(evntInfDto.getF_PRCS())
                    .setF_TEST(evntInfDto.getF_TEST())
                    .setStartTime(calendar.getTime());
            evntInfVo.setEndTime(nowDate);
            evntInfVo.setDbType(InitConfig.getDriverType());
            List<EVNT_INF_DTO> evntInfDtoList = evntInfService.getList(evntInfVo);
            causeEvntInfDtoList = evntInfDtoList.stream().filter(s -> StringUtils.isNotEmpty(s.getRtcsName())).collect(Collectors.toList());
            measuresEvntInfDtoList = evntInfDtoList.stream().filter(s -> StringUtils.isNotEmpty(s.getRsatName())).collect(Collectors.toList());
        }
        Map<String, Long> causeMap =
                causeEvntInfDtoList.stream().collect(Collectors.groupingBy(EVNT_INF_DTO::getRtcsName, Collectors.counting()));
        Map<String, Long> measuresMap =
                measuresEvntInfDtoList.stream().collect(Collectors.groupingBy(EVNT_INF_DTO::getRsatName, Collectors.counting()));

        // 获得 TaskExtDO Map
        List<BPM_TASK_DTO> bpmTaskExtDOs = selectListByTaskIds(convertSet(tasks, HistoricTaskInstance::getId));
        Map<String, BPM_TASK_DTO> bpmTaskExtDOMap = convertMap(bpmTaskExtDOs, BPM_TASK_DTO::getF_TASK_NUM);
        // 获得 ProcessInstance Map
        HistoricProcessInstance processInstance = processInstanceService.getHistoricProcessInstance(processInstanceId);
        // 获得 User Map
        Set<String> userIds = new HashSet<>();
        tasks.forEach(historicTaskInstance -> {
            String[] split = historicTaskInstance.getAssignee().split(Constants.COMMA);
            userIds.addAll(List.of(split));
        });
        userIds.add(processInstance.getStartUserId());
        EMPL_INF_VO emplInfVo = new EMPL_INF_VO();
        emplInfVo.setIds(new ArrayList<>(userIds).stream()
                .filter(StringUtils::isNotEmpty).map(Long::valueOf)
                .collect(Collectors.toList()));
        R<Map<Long, AdminUserRespDTO>> r = remoteUserService.getUserMap(emplInfVo);
        Map<Long, AdminUserRespDTO> userMap = new HashMap<>();
        if (r.getData() != null)
            userMap = r.getData();


        // 拼接数据
        List<BpmTaskRespVO> bpmTaskRespVOS = BpmTaskConvert.INSTANCE.convertList3(tasks, bpmTaskExtDOMap, processInstance, userMap, new HashMap<>());
        bpmTaskRespVOS.forEach(bpmTaskRespVO -> {
            if (CollectionUtils.isNotEmpty(bpmTaskRespVO.getCandidateUserIds())) {
                emplInfVo.setIds(new ArrayList<>(bpmTaskRespVO.getCandidateUserIds()).stream()
                        .filter(StringUtils::isNotEmpty).map(Long::valueOf)
                        .collect(Collectors.toList()));
                R<Map<Long, AdminUserRespDTO>> candidateMap = remoteUserService.getUserMap(emplInfVo);
                if (candidateMap.getData() != null) {
                    List<String> arrayList = new ArrayList<>();
                    candidateMap.getData().forEach((k, v) -> {
                        arrayList.add(v.getNickname());
                    });
                    bpmTaskRespVO.setCandidateUsers(arrayList);
                }
            }
        });
//        Map<String, BpmTaskRespVO> bpmTaskRespVOMap = convertMap(bpmTaskRespVOS, BpmTaskRespVO::getDefinitionKey);
        Map<String, List<BpmTaskRespVO>> bpmTaskRespVOMap = bpmTaskRespVOS.stream().collect(Collectors.groupingBy(BpmTaskRespVO::getDefinitionKey,
                LinkedHashMap::new, Collectors.toList()));
        Map<String, BPM_TASK_DTO> startMap = new HashMap<>();
        /*获取开始节点任务*/
        if (CollectionUtils.isNotEmpty(bpmTaskExtDOs)) {
            List<BPM_TASK_DTO> bpmTaskDtos = selectStartTask(bpmTaskExtDOs.get(0).getF_IDENTIFICATION());
            if (CollectionUtils.isNotEmpty(bpmTaskDtos)) {
                startMap = convertMap(bpmTaskDtos, BPM_TASK_DTO::getF_NAME);
            }
        }
        List<BpmTaskRespVO> taskList = getTaskList(processInstanceId);
        List<BpmTaskRespVO> list = new ArrayList<>();
        Map<String, BPM_TASK_DTO> finalStartMap = startMap;

        taskList.forEach(bpmTaskRespVO -> {
            if (bpmTaskRespVOMap.get(bpmTaskRespVO.getDefinitionKey()) != null) {
                BpmTaskRespVO vo;
                if (bpmTaskRespVOMap.get(bpmTaskRespVO.getDefinitionKey()).size() == 1) {
                    vo = bpmTaskRespVOMap.get(bpmTaskRespVO.getDefinitionKey()).get(0);
                } else {
                    /*获取未处理的节点信息*/
                    List<BpmTaskRespVO> collect =
                            bpmTaskRespVOMap.get(bpmTaskRespVO.getDefinitionKey()).stream().filter(t -> t.getEndTime() == null).collect(Collectors.toList());
                    List<BpmTaskRespVO> voList = bpmTaskRespVOMap.get(bpmTaskRespVO.getDefinitionKey()).stream().filter(t -> t.getEndTime() != null)
                            .sorted(Comparator.comparing(BpmTaskRespVO::getEndTime).reversed()).collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(collect)) {
                        vo = collect.get(0);
                        vo.setSubTasks(voList);
                    } else {
                        vo = voList.get(0);
                        voList.remove(0);
                        vo.setSubTasks(voList);
                    }
                }
                if (StringUtils.isEmpty(vo.getFields()))
                    vo.setFields(bpmTaskRespVO.getFields());
//                vo.setCandidateUsers(bpmTaskRespVO.getCandidateUsers());
                list.add(vo);
            } else if (finalStartMap.get(bpmTaskRespVO.getName()) != null) {
                bpmTaskRespVO.setReason(finalStartMap.get(bpmTaskRespVO.getName()).getF_REASON());
                bpmTaskRespVO.setEvntInfDto(evntInfDto);
                bpmTaskRespVO.setCauseMap(causeMap);
                bpmTaskRespVO.setMeasuresMap(measuresMap);
                list.add(bpmTaskRespVO);
            } else {
                list.add(bpmTaskRespVO);
            }
        });
//        bpmTaskInfoVO.setBpmTaskRespVOList(list);
        return list;
    }

    public List<BPM_TASK_DTO> selectListByTaskIds(Collection<String> list) {
        LambdaQueryWrapper<BPM_TASK> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BPM_TASK::getF_TASK_NUM, list);
        queryWrapper.eq(BPM_TASK::getF_DEL, YesOrNoEnum.NO.getType());
        List<BPM_TASK> bpmTasks = bpmTaskMapper.selectList(queryWrapper);
        return BpmTaskConvert.INSTANCE.convertList(bpmTasks);
    }

    public List<BPM_TASK_DTO> selectStartTask(String identification) {
        LambdaQueryWrapper<BPM_TASK> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_TASK::getF_IDENTIFICATION, identification).eq(BPM_TASK::getF_TASK_NUM, "0");
        queryWrapper.eq(BPM_TASK::getF_DEL, YesOrNoEnum.NO.getType());
        List<BPM_TASK> bpmTasks = bpmTaskMapper.selectList(queryWrapper);
        return BpmTaskConvert.INSTANCE.convertList(bpmTasks);
    }

    public BPM_TASK_DTO selectByTaskBum(String taskId) {
        LambdaQueryWrapper<BPM_TASK> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_TASK::getF_TASK_NUM, taskId);
        queryWrapper.eq(BPM_TASK::getF_DEL, YesOrNoEnum.NO.getType());
        BPM_TASK bpmTask = bpmTaskMapper.selectOne(queryWrapper);
        return BpmTaskConvert.INSTANCE.convert(bpmTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveTask(Long userId, @Valid BpmTaskApproveReqVO reqVO) {
        // 校验任务存在
        Task task = checkTask(userId, reqVO.getId());
        // 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        if (instance == null) {
            throw new BusinessException(DataManagementExceptionEnum.PROCESS_INSTANCE_NOT_EXISTS);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("input", "Y");
        map.put(WorkFlowConstants.userId, userId);
        // 完成任务，审批通过
        taskService.complete(task.getId(), map);

        LambdaUpdateWrapper<BPM_TASK> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(BPM_TASK::getF_TASK_NUM, task.getId())
                .eq(BPM_TASK::getF_DEL, YesOrNoEnum.NO.getType())
                .set(BPM_TASK::getF_AUDIT_USER, userId)
                .set(BPM_TASK::getF_RESULT, BpmProcessInstanceResultEnum.APPROVE.getResult())
                .set(BPM_TASK::getF_EDTM, new Date())
                .set(BPM_TASK::getF_EDUE, userId);
        if (StringUtils.isNotEmpty(reqVO.getFields())) {
            queryWrapper.set(BPM_TASK::getF_FIELDS, reqVO.getFields());
        }
        if (StringUtils.isNotEmpty(reqVO.getReason())) {
            queryWrapper.set(BPM_TASK::getF_REASON, reqVO.getReason());
        }

        // 更新任务拓展表为通过
        bpmTaskMapper.update(null, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectTask(Long userId, BpmTaskApproveReqVO reqVO) {
        Task task = checkTask(userId, reqVO.getId());
        // 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        if (instance == null) {
            throw new BusinessException(DataManagementExceptionEnum.PROCESS_INSTANCE_NOT_EXISTS);
        }
        // 获取当前的任务节点
        String activeTaskKey = task.getTaskDefinitionKey();
        BpmnModelInstance bpmnModel = bpmProcessDefinitionService.getBpmnModel(task.getProcessDefinitionId());
        List<DomElement> domElementList = bpmnModel.getDocument().getRootElement().getChildElements();
        if (org.springframework.util.CollectionUtils.isEmpty(domElementList)) {
            return;
        }
        String beforeActivity = null;
        DomElement domElement = domElementList.stream().filter(it -> "process".equals(it.getLocalName())).findFirst().orElse(null);
        HashMap<String, String> seqMap = getSequenceFlow(domElement);
        if (StrUtil.isNotBlank(seqMap.get(activeTaskKey)) && seqMap.get(activeTaskKey).contains("Gateway")) {
            beforeActivity = seqMap.get(seqMap.get(activeTaskKey));
            String[] split = beforeActivity.split(Constants.COMMA);
            for (String s : split) {
                if (s.contains("${input == 'H'}")) {
                    String[] arr = s.split("&");
                    beforeActivity = arr[0];
                }
            }
        }

        ActivityInstance tree = runtimeService.getActivityInstance(task.getProcessInstanceId());
        if (beforeActivity != null) {
            runtimeService.createProcessInstanceModification(task.getProcessInstanceId())
                    .cancelActivityInstance(getInstanceIdForActivity(tree, task.getTaskDefinitionKey()))
                    .cancelAllForActivity(task.getTaskDefinitionKey()).setAnnotation("进行了驳回到指定任务节点操作")
                    .startBeforeActivity(beforeActivity)
//                .setVariables(taskVariables)
                    .execute();
        }

        // 更新流程实例为不通过
        processInstanceService.updateProcessInstanceExtReject(instance.getProcessInstanceId(), reqVO.getReason(), beforeActivity == null);

        LambdaUpdateWrapper<BPM_TASK> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(BPM_TASK::getF_TASK_NUM, task.getId())
                .eq(BPM_TASK::getF_DEL, YesOrNoEnum.NO.getType())
                .set(BPM_TASK::getF_RESULT, BpmProcessInstanceResultEnum.REJECT.getResult())
                .set(BPM_TASK::getF_END_TIME, DateUtils.getNowDate());
        if (StringUtils.isNotEmpty(reqVO.getReason())) {
            queryWrapper.set(BPM_TASK::getF_REASON, reqVO.getReason());
        }
        queryWrapper.set(BPM_TASK::getF_EDTM, new Date());
        queryWrapper.set(BPM_TASK::getF_EDUE,userId);
        // 更新任务拓展表为不通过
        bpmTaskMapper.update(null, queryWrapper);
    }


    /**
     * 获取流程设计的所有连接线
     *
     * @param e
     * @return
     */
    public static HashMap<String, String> getSequenceFlow(DomElement e) {
        HashMap<String, String> sequenceFlow = new HashMap<>();
        e.getChildElements().stream()
                .filter(it -> "sequenceFlow".equals(it.getLocalName()))
                .forEach(item -> {
                    if (sequenceFlow.get(item.getAttribute("sourceRef")) == null) {
                        String sourceRef = item.getAttribute("sourceRef");
                        if (sourceRef.startsWith("StartEvent")) {
                            sequenceFlow.put("StartEvent", item.getAttribute("sourceRef"));
                        }
                        if (item.getAttribute("sourceRef").contains("Gateway")) {
                            sequenceFlow.put(sourceRef, item.getAttribute("targetRef") + "&" + item.getTextContent());
                        } else {
                            sequenceFlow.put(sourceRef, item.getAttribute("targetRef"));
                        }
                    } else {
                        if (item.getAttribute("sourceRef").contains("Gateway")) {
                            sequenceFlow.put(item.getAttribute("sourceRef"), sequenceFlow.get(item.getAttribute("sourceRef")) + Constants.COMMA + item.getAttribute("targetRef") + "&" + item.getTextContent());
                        }
                    }
                });
        return sequenceFlow;
    }

    private String getInstanceIdForActivity(ActivityInstance activityInstance, String activityId) {
        ActivityInstance instance = getChildInstanceForActivity(activityInstance, activityId);
        if (instance != null) {
            return instance.getId();
        }
        return null;
    }

    private ActivityInstance getChildInstanceForActivity(ActivityInstance activityInstance, String activityId) {
        if (activityId.equals(activityInstance.getActivityId())) {
            return activityInstance;
        }
        for (ActivityInstance childInstance : activityInstance.getChildActivityInstances()) {
            ActivityInstance instance = getChildInstanceForActivity(childInstance, activityId);
            if (instance != null) {
                return instance;
            }
        }
        return null;
    }

    @Override
    public void updateTaskAssignee(Long userId, BpmTaskApproveReqVO reqVO) {
        // 校验任务存在
        Task task = checkTask(userId, reqVO.getId());
        // 更新负责人
        updateTaskAssignee(task.getId(), reqVO.getAssigneeUserId());
    }

    @Override
    public void updateTaskAssignee(String id, String userId) {
        taskService.setAssignee(id, String.valueOf(userId));
    }

    /**
     * 校验任务是否存在， 并且是否是分配给自己的任务
     *
     * @param userId 用户 id
     * @param taskId task id
     */
    private Task checkTask(Long userId, String taskId) {
        Task task = getTask(taskId);
        if (task == null) {
            throw new BusinessException(DataManagementExceptionEnum.TASK_COMPLETE_FAIL_NOT_EXISTS);
        }
        if (!task.getAssignee().contains(String.valueOf(userId))) {
            throw new BusinessException(DataManagementExceptionEnum.TASK_COMPLETE_FAIL_ASSIGN_NOT_SELF);
        }
        return task;
    }

    @Override
    public void createTaskExt(CamundaTaskDTO task) {
        BPM_TASK taskExtDO =
                BpmTaskConvert.INSTANCE.convert2TaskExt(task).setF_RESULT(BpmProcessInstanceResultEnum.PROCESS.getResult());
        bpmTaskMapper.insert(taskExtDO);
    }

    @Override
    public void createTaskExt(BPM_TASK task) {
        bpmTaskMapper.insert(task);
    }

    @Override
    public void updateTaskExtComplete(CamundaTaskDTO task) {
        LambdaUpdateWrapper<BPM_TASK> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(BPM_TASK::getF_TASK_NUM, task.getTaskId())
                .set(BPM_TASK::getF_RESULT, BpmProcessInstanceResultEnum.APPROVE.getResult())
                .set(BPM_TASK::getF_END_TIME, DateUtils.getNowDate())
                .set(BPM_TASK::getF_EDTM, new Date());
        bpmTaskMapper.update(null, queryWrapper);
    }

    @Override
    public void updateTaskExtCancel(String taskId) {
        // 需要在事务提交后，才进行查询。不然查询不到历史的原因
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {

            @Override
            public void afterCommit() {
                // 可能只是活动，不是任务，所以查询不到
                HistoricTaskInstance task = getHistoricTask(taskId);
                if (task == null) {
                    return;
                }

                // 如果任务拓展表已经是完成的状态，则跳过
                BPM_TASK_DTO taskExt = selectByTaskBum(taskId);
                if (taskExt == null) {
                    log.error("[updateTaskExtCancel][taskId({}) 查找不到对应的记录，可能存在问题]", taskId);
                    return;
                }
                // 如果已经是最终的结果，则跳过
                if (BpmProcessInstanceResultEnum.isEndResult(taskExt.getF_RESULT())) {
                    log.error("[updateTaskExtCancel][taskId({}) 处于结果({})，无需进行更新]", taskId, taskExt.getF_RESULT());
                    return;
                }

                LambdaUpdateWrapper<BPM_TASK> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(BPM_TASK::getF_TASK, taskExt.getF_TASK())
                        .set(BPM_TASK::getF_RESULT, BpmProcessInstanceResultEnum.CANCEL.getResult())
                        .set(BPM_TASK::getF_END_TIME, DateUtils.getNowDate())
                        .set(BPM_TASK::getF_REASON, BpmProcessInstanceDeleteReasonEnum.translateReason(task.getDeleteReason()))
                        .set(BPM_TASK::getF_EDTM, new Date());
                // 更新任务
                bpmTaskMapper.update(null, updateWrapper);
            }

        });
    }

    @Override
    public void updateTaskExtAssign(CamundaTaskDTO task) {
        LambdaUpdateWrapper<BPM_TASK> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(BPM_TASK::getF_TASK_NUM, task.getTaskId())
                .set(BPM_TASK::getF_ASSIGNEE_USER, task.getAssigneeUserId())
                .set(BPM_TASK::getF_EDTM, new Date());
        bpmTaskMapper.update(null, queryWrapper);
        // 发送通知。在事务提交时，批量执行操作，所以直接查询会无法查询到 ProcessInstance，所以这里是通过监听事务的提交来实现。
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                ProcessInstance processInstance =
                        processInstanceService.getProcessInstance(task.getProcessInstanceId());
                R<AdminUserRespDTO> user = remoteUserService.getUser(task.getProcessStartUserId());
                AdminUserRespDTO startUser = new AdminUserRespDTO();
                if (user.getData() != null) {
                    startUser = user.getData();
                }
//                messageService.sendMessageWhenTaskAssigned(
//                        BpmTaskConvert.INSTANCE.convert(processInstance, startUser, task));
            }
        });
    }

    @Override
    public List<BpmTaskRespVO> getTaskList(String processInstanceId) {
        LambdaQueryWrapper<BPM_PROCESS_INSTANCE> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPM_PROCESS_INSTANCE::getF_PROCESS_INSTANCE, processInstanceId);
        BPM_PROCESS_INSTANCE processInstance = processInstanceService.getOne(queryWrapper);

        LambdaQueryWrapper<BPM_PROCESS_DEFINITION> definitionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        definitionLambdaQueryWrapper.eq(BPM_PROCESS_DEFINITION::getF_PROCESS_DEFINITION, processInstance.getF_PROCESS_DEFINITION());
        BPM_PROCESS_DEFINITION bpmProcessDefinition = bpmProcessDefinitionService.getOne(definitionLambdaQueryWrapper);

        /*耗时*/
        long start = System.currentTimeMillis();
        List<BPM_TASK_RULE_DTO> taskAssignFormList =
                bpmTaskAssignRuleService.getTaskAssignFormList(bpmProcessDefinition.getF_MODE(),
                        bpmProcessDefinition.getF_PROCESS_DEFINITION());
        long end = System.currentTimeMillis();
        log.info("[getTaskList][processInstanceId({}) 查询任务配置耗时：{}ms]", processInstanceId, end - start);
        List<BpmTaskRespVO> list = new ArrayList<>();
        IntStream.range(0, taskAssignFormList.size()).forEach(index -> {
            BPM_TASK_RULE_DTO bpmTaskRuleDto = taskAssignFormList.get(index);
            boolean isLast = index == taskAssignFormList.size() - 1;
            BpmTaskRespVO bpmTaskRespVO = new BpmTaskRespVO();
            bpmTaskRespVO.setDefinitionKey(bpmTaskRuleDto.getF_TASK_KEY());
            bpmTaskRespVO.setName(bpmTaskRuleDto.getTaskDefinitionName());
            bpmTaskRespVO.setCreateTime(processInstance.getF_CRTM());
            bpmTaskRespVO.setResult(BpmProcessInstanceResultEnum.PROCESS.getResult());
            bpmTaskRespVO.setNum(bpmTaskRuleDto.getNum());
            //最后一个节点 也就是结束节点 添加结束时间 = 流程结束时间
            if(isLast){
                bpmTaskRespVO.setEndTime(processInstance.getF_END_TIME());
            }
            if (bpmTaskRuleDto.getNum().equals(WorkFlowConstants.startTaskNum)) {
                bpmTaskRespVO.setResult(BpmProcessInstanceResultEnum.APPROVE.getResult());
            }
            /*获取候选审核用户*/
//            Set<String> ruleUsers = bpmTaskAssignRuleService.getRuleUsers(bpmTaskRuleDto);
//            if (CollectionUtils.isNotEmpty(ruleUsers)) {
//                EMPL_INF_VO emplInfVo = new EMPL_INF_VO();
//                emplInfVo.setIds(new ArrayList<>(ruleUsers));
//                R<Map<String, AdminUserRespDTO>> userMap = remoteUserService.getUserMap(emplInfVo);
//                if (userMap.getData() != null) {
//                    List<String> arrayList = new ArrayList<>();
//                    userMap.getData().forEach((k, v) -> {
//                        arrayList.add(v.getNickname());
//                    });
//                    bpmTaskRespVO.setCandidateUsers(arrayList);
//                }
//            }

            if (StringUtils.isNotEmpty(bpmTaskRuleDto.getF_FROM())) {
                BPM_FROM_DTO form = bpmFormService.getForm(bpmTaskRuleDto.getF_FROM());
                if (form != null)
                    bpmTaskRespVO.setFields(form.getF_FIELDS());
            }
            list.add(bpmTaskRespVO);
        });
        return list;
    }

    @Override
    public void returns(Long userId, BpmTaskApproveReqVO reqVO) {
        Task task = getTask(reqVO.getId());

        runtimeService.createProcessInstanceModification(task.getProcessInstanceId())
                .cancelActivityInstance(task.getProcessInstanceId())//关闭相关任务
                .setAnnotation("进行了驳回到上一个任务节点操作")
                .startBeforeActivity(reqVO.getTargetNodeId())//启动目标活动节点
//                .setVariables(map)//流程的可变参数赋值
                .execute();
    }

    @Override
    public EVNT_INF_DTO getEvntInf(String processInstanceId) {
        BPM_PROCESS_INSTANCE instance = processInstanceService.getById(processInstanceId);
        if (instance == null)
            return null;
        if (instance.getF_EVNT() == null)
            return null;

        return evntInfService.getInfo(instance.getF_EVNT());
    }

    private Task getTask(String id) {
        return taskService.createTaskQuery().taskId(id).singleResult();
    }

    private HistoricTaskInstance getHistoricTask(String id) {
        return historyService.createHistoricTaskInstanceQuery().taskId(id).singleResult();
    }

}
