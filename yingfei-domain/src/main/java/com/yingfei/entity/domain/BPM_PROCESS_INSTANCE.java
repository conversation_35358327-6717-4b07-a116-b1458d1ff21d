package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 工作流的流程实例的拓展
 * @TableName BPM_PROCESS_INSTANCE
 */
@TableName(value ="BPM_PROCESS_INSTANCE")
@Accessors(chain = true)
@Data
public class BPM_PROCESS_INSTANCE extends BaseEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_BPIE;

    /**
     * 流程实例的名字
     */
    private String F_NAME;

    /**
     * 发起流程的用户编号
     */
    private Long F_START_USER;

    /**
     * 流程定义的编号
     */
    private String F_PROCESS_DEFINITION;

    /**
     * 流程实例的编号
     */
    private String F_PROCESS_INSTANCE;

    /**
     * 流程分类
     */
    private String F_CATEGORY;

    /**
     * 流程实例的状态
     */
    private Integer F_STATUS;

    /**
     * 流程实例的结果
     */
    private Integer F_RESULT;

    /**
     * 结束时间
     */
    private Date F_END_TIME;

    /**
     * 表单值
     */
    private String F_FROM_VAL;

    /**
     * 是否删除(0:否 1:是)
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 创建用户
     */
    private Long F_CRUE;

    /**
     * 修改用户
     */
    private Long F_EDUE;

    /**
     * 报警表id
     */
    private Long F_EVNT;
}
