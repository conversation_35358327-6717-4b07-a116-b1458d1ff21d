package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存班次信息表
 * @TableName SHIFT_DAT
 */
@TableName(value ="SHIFT_DAT")
@Data
public class SHIFT_DAT extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_SHIFT;

    /**
     * 班次所关联的班次组ID(SHIFT_GRP)
     */
    private Long F_SHGP = 0L;

    /**
     * 班次名称
     */
    private String F_NAME;

    /**
     * 班次因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;


    public static SHIFT_DAT init() {
        SHIFT_DAT data = new SHIFT_DAT();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }

}
