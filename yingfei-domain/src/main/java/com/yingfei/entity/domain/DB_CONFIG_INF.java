package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据库配置
 *
 * @TableName DB_CONFIG_INF
 */
@TableName(value = "DB_CONFIG_INF")
@Data
public class DB_CONFIG_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_DBCO;

    /**
     * 数据库名称
     */
    private String F_NAME;

    /**
     * 数据库主链接
     */
    private String F_PREFIX;

    /**
     * 数据库驱动
     */
    private String F_DRIVER;

    /**
     * 数据库链接表名配置
     */
    private String F_SUFFIX;

    /**
     * 获取所有数据库SQL
     */
    private String F_DB_SQL;

    /**
     * 记录创建用户ID
     */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

    /**
     * 数据库账号
     */
    private String F_USERNAME;

    /**
     * 数据库密码
     */
    private String F_PASSWORD;

    /**
     * 数据库类型(1:sqlserver 2:oracle 3:mysql)
     */
    private Integer F_TYPE = 1;

    public static DB_CONFIG_INF init() {
        DB_CONFIG_INF data = new DB_CONFIG_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
