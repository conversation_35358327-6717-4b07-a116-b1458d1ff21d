package com.yingfei.entity.domain;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 消息发送配置表
 */
@TableName(value = "SEND_CONFIG_INF")
@Data
public class SEND_CONFIG_INF extends BaseEntity {

    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ID;

    private String F_NAME;

    private Integer F_TYPE;

    private String F_DATA;

    private Long F_CRUE;

    private Long F_EDUE;

    @TableField(exist = false)
    private JSONObject dataJson;
}
