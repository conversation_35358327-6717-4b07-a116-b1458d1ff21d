<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yingfei</groupId>
    <artifactId>YINGFEI-ADMIN</artifactId>
    <version>1.0.0</version>

    <name>yingfei</name>

    <properties>
        <yingfei.version>1.0.0</yingfei.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- 编译时的编码 这里就是你运行项目，会给你的文件进行编码-->
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>11</java.version>
        <spring-boot.version>2.7.7</spring-boot.version>
        <spring-cloud.version>2021.0.5</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.4.0</spring-cloud-alibaba.version>
        <!--        <spring-cloud-alibaba.version>2.0.2.RELEASE</spring-cloud-alibaba.version>-->
        <spring-boot-admin.version>2.7.10</spring-boot-admin.version>
        <knife4j.version>3.0.3</knife4j.version>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>1.6.2</swagger.core.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>1.4.6</pagehelper.boot.version>
        <druid.version>1.2.18</druid.version>
        <dynamic-ds.version>3.5.2</dynamic-ds.version>
        <commons.io.version>2.11.0</commons.io.version>
        <velocity.version>2.3</velocity.version>
        <fastjson.version>2.0.58</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>5.2.3</poi.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <hutool.version>5.8.20</hutool.version>
        <mybatis-plus-spring-boot-starter.version>3.5.2</mybatis-plus-spring-boot-starter.version>
        <mybatis-plus-join-boot-starter.version>1.4.11</mybatis-plus-join-boot-starter.version>
        <!--        <aliyun-sms.version>2.0.16</aliyun-sms.version>-->
        <thumbnailator.version>0.4.8</thumbnailator.version>
        <pingyin.version>2.5.1</pingyin.version>
        <netty.version>4.1.42.Final</netty.version>
        <aliyun-sms.version>2.0.16</aliyun-sms.version>
        <aliyun-java-sdk.version>4.5.17</aliyun-java-sdk.version>
        <mssql-jdbc.version>9.4.0.jre8</mssql-jdbc.version>
        <commons-jexl3.version>3.1</commons-jexl3.version>
        <xxl-job-core.version>2.3.1</xxl-job-core.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <google.guava.version>32.1.3-jre</google.guava.version>
        <java.mail.version>1.6.2</java.mail.version>
        <jimureport.version>1.9.1</jimureport.version>
        <commons-csv.version>1.9.0</commons-csv.version>
        <postgresql.version>42.5.0</postgresql.version>
        <oracle.version>23.5.0.24.07</oracle.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- FastDFS 分布式文件系统 -->
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${tobato.version}</version>
            </dependency>

            <!-- Swagger 依赖配置 -->
            <!--            <dependency>-->
            <!--                <groupId>io.swagger</groupId>-->
            <!--                <artifactId>swagger-models</artifactId>-->
            <!--                <version>${swagger.core.version}</version>-->
            <!--            </dependency>-->
            <!--            <dependency>-->
            <!--                <groupId>io.swagger</groupId>-->
            <!--                <artifactId>swagger-annotations</artifactId>-->
            <!--                <version>${swagger.core.version}</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!-- FastJSON2 Spring 集成库 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2-extension-spring6</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>com.yingfei</groupId>
                <artifactId>yingfei-common-core</artifactId>
                <version>${yingfei.version}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.yingfei</groupId>
                <artifactId>yingfei-common-swagger</artifactId>
                <version>${yingfei.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.yingfei</groupId>
                <artifactId>yingfei-common-security</artifactId>
                <version>${yingfei.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.yingfei</groupId>
                <artifactId>yingfei-common-datascope</artifactId>
                <version>${yingfei.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.yingfei</groupId>
                <artifactId>yingfei-common-datasource</artifactId>
                <version>${yingfei.version}</version>
            </dependency>

            <!-- 分布式事务 -->
            <dependency>
                <groupId>com.yingfei</groupId>
                <artifactId>yingfei-common-seata</artifactId>
                <version>${yingfei.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.yingfei</groupId>
                <artifactId>yingfei-common-log</artifactId>
                <version>${yingfei.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.yingfei</groupId>
                <artifactId>yingfei-common-redis</artifactId>
                <version>${yingfei.version}</version>
            </dependency>

            <!-- 流程服务 -->
            <dependency>
                <groupId>com.yingfei</groupId>
                <artifactId>yingfei-common-bpm</artifactId>
                <version>${yingfei.version}</version>
            </dependency>

            <!-- 定时任务服务 -->
            <dependency>
                <groupId>com.yingfei</groupId>
                <artifactId>yingfei-common-schedule</artifactId>
                <version>${yingfei.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.yingfei</groupId>
                <artifactId>yingfei-api-system</artifactId>
                <version>${yingfei.version}</version>
            </dependency>

            <!-- 数据接口 -->
            <dependency>
                <groupId>com.yingfei</groupId>
                <artifactId>yingfei-api-dataManagement</artifactId>
                <version>${yingfei.version}</version>
            </dependency>


            <!-- hutool工具类 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!--            <dependency>-->
            <!--                <groupId>com.fasterxml.jackson.datatype</groupId>-->
            <!--                <artifactId>jackson-datatype-joda</artifactId>-->
            <!--                <version>${jackson.version}</version>-->
            <!--            </dependency>-->

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis-plus-join-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus-spring-boot-starter.version}</version>
            </dependency>

            <!--            <dependency>-->
            <!--                <groupId>com.aliyun</groupId>-->
            <!--                <artifactId>dysmsapi20170525</artifactId>-->
            <!--                <version>${aliyun-sms.version}</version>-->
            <!--            </dependency>-->

            <!--thumbnailator图片处理-->
            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>${thumbnailator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pingyin.version}</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>

            <!--aliyun 短信-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${aliyun-sms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun-java-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql-jdbc.version}</version>
            </dependency>

            <!-- Oracle Connector -->
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc11</artifactId>
                <version>${oracle.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>${java.mail.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-jexl3</artifactId>
                <version>${commons-jexl3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${google.guava.version}</version>
            </dependency>

            <!--积木报表依赖-->
            <dependency>
                <groupId>org.jeecgframework.jimureport</groupId>
                <artifactId>jimureport-spring-boot-starter</artifactId>
                <version>${jimureport.version}</version>
            </dependency>
            <!--积木 BI 依赖-->
            <dependency>
                <groupId>org.jeecgframework.jimureport</groupId>
                <artifactId>jimubi-spring-boot-starter</artifactId>
                <version>${jimureport.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>${commons-csv.version}</version>
            </dependency>

            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version> <!-- 可根据需要调整版本 -->
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>yingfei-auth</module>
        <module>yingfei-gateway</module>
        <!--        <module>yingfei-visual</module>-->
        <module>yingfei-api</module>
        <module>yingfei-common</module>
        <module>yingfei-modules</module>
        <module>yingfei-domain</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- hutool工具类 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <!-- 用于读取配置文件注解@ConfigurationProperties-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- druid数据源驱动 -->
        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>druid-spring-boot-starter</artifactId>-->
        <!--            <version>${druid.version}</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
