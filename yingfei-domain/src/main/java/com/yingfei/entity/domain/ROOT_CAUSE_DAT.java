package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存异常原因信息表
 * @TableName ROOT_CAUSE_DAT
 */
@TableName(value ="ROOT_CAUSE_DAT")
@Data
public class ROOT_CAUSE_DAT extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_RTCS;

    /**
     * 异常原因所关联的异常原因组ID
     */
    private Long F_RCGP = 0L;

    /**
     * 异常原因名称
     */
    private String F_NAME;

    /**
     * 异常原因因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    /**
     * 缺陷图片
     */
    private String F_IMAGE;

    public static ROOT_CAUSE_DAT init() {
        ROOT_CAUSE_DAT data = new ROOT_CAUSE_DAT();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
