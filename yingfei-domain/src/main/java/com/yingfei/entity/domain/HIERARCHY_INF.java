package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 层级结构表
 * @TableName HIERARCHY_INF
 */
@TableName(value ="HIERARCHY_INF")
@Data
public class HIERARCHY_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_HIER;

    /**
     * 部门名称
     */
    private String F_NAME;

    /**
     * 父级主键
     */
    private Long F_PARENT;

    /**
     * 层级类型(0:集团 1:事业部  2:工厂  3:部门  4:车间)
     */
    private Integer F_TYPE = 0;

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;


    public static HIERARCHY_INF init() {
        HIERARCHY_INF data = new HIERARCHY_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }

}
