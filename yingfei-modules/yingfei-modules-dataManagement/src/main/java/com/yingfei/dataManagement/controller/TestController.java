package com.yingfei.dataManagement.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.microsoft.sqlserver.jdbc.SQLServerDriver;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.constant.WorkFlowConstants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.Timing.CapabilityTrendTiming;
import com.yingfei.dataManagement.mapper.ACT_GE_BYTEARRAYMapper;
import com.yingfei.dataManagement.mapper.BpmProcessInstanceExtMapper;
import com.yingfei.dataManagement.service.*;
import com.yingfei.dataManagement.service.bpm.BpmProcessInstanceService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.CellRowAndColDTO;
import com.yingfei.entity.dto.DataValConfigDTO;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.enums.DataValTypeEnum;
import com.yingfei.entity.enums.SpecialHandlingTypeEnum;
import com.yingfei.entity.vo.BPM_PROCESS_INSTANCE_VO;
import com.yingfei.entity.vo.SerialDebuggingVO;
import com.yingfei.entity.vo.SubgroupFilterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.*;
import java.nio.file.attribute.FileTime;
import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "测试")
@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private SGRP_VALService sgrpValService;
    @Resource
    private EVNT_INFService evntInfService;
    @Resource
    private BpmProcessInstanceService bpmProcessInstanceService;
    @Resource
    private BpmProcessInstanceExtMapper processInstanceExtMapper;
    @Resource
    private SGRP_INF_AService sgrpInfAService;
    @Resource
    private ACT_GE_BYTEARRAYMapper actGeBytearrayMapper;
    @Resource
    private CapabilityTrendTiming capabilityTrendTiming;
    @Resource
    private TEMP_TABLE_INFService tempTableInfService;

    private final static Pattern pattern = Pattern.compile("<(.*?)>");


    @GetMapping("/test")
    @ApiOperation("测试触发报警流程")
    public R<?> test() {
        /*获取子组*/
        SGRP_INF sgrpInf = sgrpInfService.getById("1804101479539060738");
        LambdaQueryWrapper<SGRP_VAL> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SGRP_VAL::getF_SGRP, sgrpInf.getF_SGRP());
        List<SGRP_VAL> sgrpValList = sgrpValService.list(queryWrapper);
        AtomicInteger i = new AtomicInteger(1);
//        sgrpValList.forEach(sgrpVal -> {
        SGRP_VAL sgrpVal = sgrpValList.get(0);
        JSONArray list = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", "测试:" + sgrpVal.getF_TEST() + "-->大于USL,数量:" + i.get());
        jsonObject.put("type", 1);
        list.add(jsonObject);
        i.incrementAndGet();
        EVNT_INF evntInf = new EVNT_INF();
        evntInf.setF_SGRP(sgrpInf.getF_SGRP())
                .setF_PART(sgrpInf.getF_PART())
                .setF_PRCS(sgrpInf.getF_PRCS())
                .setF_TEST(sgrpVal.getF_TEST())
                .setF_DATA(list.toJSONString())
                .setF_EVTM(DateUtils.getNowDate())
                .setF_SGTM(sgrpInf.getF_SGTM())
                .setF_CRUE(SecurityUtils.getUserId())
                .setF_EDUE(SecurityUtils.getUserId())
        ;
        evntInfService.save(evntInf);

        /*生成流程*/
        BPM_PROCESS_INSTANCE_VO bpmProcessInstanceVo = new BPM_PROCESS_INSTANCE_VO();
        bpmProcessInstanceVo.setF_PROCESS_DEFINITION("Alarm:4:2c0ee0e5-4a56-11ef-91c6-00ff2d0a963d");
        Map<String, Object> map = new HashMap<>();
        map.put(WorkFlowConstants.ALARMS_DESCRIBE, sgrpVal.getF_TEST() + "大于USL" + i.get());
        bpmProcessInstanceVo.setVariables(map);
        String processInstance = bpmProcessInstanceService.createProcessInstance(bpmProcessInstanceVo);
        LambdaQueryWrapper<BPM_PROCESS_INSTANCE> bpmProcessInstanceLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bpmProcessInstanceLambdaQueryWrapper.eq(BPM_PROCESS_INSTANCE::getF_PROCESS_INSTANCE, processInstance);

        BPM_PROCESS_INSTANCE bpmProcessInstance = processInstanceExtMapper.selectOne(bpmProcessInstanceLambdaQueryWrapper);
        evntInfService.updateById(evntInf);
//        });

        return R.ok();
    }

    @GetMapping("/test2")
    public void testCreate(){

        BPM_PROCESS_INSTANCE_VO bpmProcessInstanceVo = new BPM_PROCESS_INSTANCE_VO();
        bpmProcessInstanceVo.setF_PROCESS_DEFINITION("Process_0x16voz:1:e88f29c3-8d28-11f0-912d-3005058aa048");
        Map<String, Object> map = new HashMap<>();
        map.put("aduit1","1942098016219426818");
        map.put("aduit2","1942098016219426818");
        map.put("audits",Collections.singletonList("1942098016219426818"));
        bpmProcessInstanceVo.setVariables(map);
        String processInstance = bpmProcessInstanceService.createProcessInstance(bpmProcessInstanceVo);
    }

    /**
     * 串口调试
     */
    @ApiOperation("串口调试接收数据")
    @PostMapping("/serialDebugging")
    public R<?> serialDebugging(@RequestBody SerialDebuggingVO serialDebuggingVO) {
        return R.ok();
    }

    /**
     * 判断是否为数子
     *
     * @param strNum
     * @return
     */
    public static boolean isNumeric(String strNum) {
        if (strNum == null) {
            return false;
        }
        return strNum.matches("-?\\d+(\\.\\d+)?");
    }

    /**
     * 保存量具名称
     */
    @ApiOperation("保存量具名称")
    @GetMapping("/saveMeasureName")
    public R<?> saveMeasureName(String deviceName, String name) {
        return R.ok();
    }

    /**
     * 根据id获取量具信息
     */
    @ApiOperation("根据id获取量具信息")
    @GetMapping("/findByMeasureId")
    public R<?> findByMeasureId(String id) {
        return R.ok();
    }

    /**
     * 获取量具配置
     */
    @ApiOperation("获取量具配置")
    @GetMapping("/getMeasureConfig")
    public R<?> getMeasureConfig(String id) {
        return R.ok();
    }

    /**
     * 数据库取值
     */
    @ApiOperation("数据库取值")
    @GetMapping("/getDatabaseHistoricalVal")
    public R<?> getDatabaseHistoricalVal(@RequestBody DataValConfigDTO dataValConfigDTO) {
        if (MapUtils.isEmpty(dataValConfigDTO.getMap()))
            return R.fail("查询条件未配置");
        SubgroupFilterVO subgroupFilterVO = new SubgroupFilterVO();

        /*测试编号单独判断*/
        AtomicReference<String> testNum = new AtomicReference<>();
        dataValConfigDTO.getMap().forEach((k, v) -> {
            Object o = v.get("id");
            if (o == null) throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
            String id = o.toString();
            switch (DataValTypeEnum.getType(Integer.valueOf(k))) {
                case STAFF:
                    subgroupFilterVO.setF_CRUE(Long.valueOf(id));
                    break;
                case PRCS_DAT:
                    subgroupFilterVO.setPrcsList(Collections.singletonList(id));
                    break;
                case PART_DAT:
                    subgroupFilterVO.setPartList(Collections.singletonList(id));
                    break;
                case LOT:
                    subgroupFilterVO.setLotList(Collections.singletonList(id));
                    break;
                case TEST_DAT:
                    subgroupFilterVO.setTestList(Collections.singletonList(id));
                    break;
                case TEST_NO:
                    testNum.set(id);
            }
        });

        /*检查缓存表数据是否满足时间条件*/
        SGRP_INF_A sgrpInfMin = sgrpInfAService.getCacheMaxMinTime(false, 1);
        Date endDate = DateUtils.getNowDate();
        Date startDate;
        if (dataValConfigDTO.getTimeType() == 0) {
            startDate = DateUtils.addSeconds(endDate, -dataValConfigDTO.getTimeNum());
        } else if (dataValConfigDTO.getTimeType() == 1) {
            startDate = DateUtils.addMinutes(endDate, -dataValConfigDTO.getTimeNum());
        } else if (dataValConfigDTO.getTimeType() == 2) {
            startDate = DateUtils.addHours(endDate, -dataValConfigDTO.getTimeNum());
        } else {
            startDate = DateUtils.addDays(endDate, -dataValConfigDTO.getTimeNum());
        }
        subgroupFilterVO.setEndDate(endDate);
        subgroupFilterVO.setStartDate(startDate);
        List<SubgroupDataDTO> subgroupDataDTOList;
        /*判断开始时间是否在最小时间范围内 */
        if (startDate.getTime() > sgrpInfMin.getF_SGTM().getTime()) {
            /*在范围内 查询缓存表*/
            subgroupDataDTOList = sgrpInfAService.getList(subgroupFilterVO);
        } else {
            /*查询主表*/
            subgroupDataDTOList = sgrpInfService.getFilterList(subgroupFilterVO);
        }

        if (CollectionUtils.isEmpty(subgroupDataDTOList)) {
            /*没有数据*/
            if (dataValConfigDTO.getNullReturn() != null) {
                return R.ok(dataValConfigDTO.getNullReturn());
            } else {
                return R.ok();
            }
        }


        subgroupDataDTOList = subgroupDataDTOList.stream()
                .sorted(Comparator.comparing(SubgroupDataDTO::getF_SGTM).reversed()).collect(Collectors.toList());
        /*最后子组*/
        SubgroupDataDTO endSubgroupDataDTO = subgroupDataDTOList.get(0);
        SGRP_VAL_CHILD_DTO endValChildDto =
                JSONObject.parseObject(endSubgroupDataDTO.getTestData(), SGRP_VAL_CHILD_DTO.class);
        if (dataValConfigDTO.getStandardType() == 0) {
            List<SGRP_VAL_CHILD_DTO.Test> testList = endValChildDto.getTestList();
            if (StringUtils.isNotEmpty(testNum.get())) {
                List<SGRP_VAL_CHILD_DTO.Test> collect =
                        testList.stream().filter(t -> t.getTestNo().toString().equals(testNum.get()))
                                .collect(Collectors.toList());
                endValChildDto.setTestList(collect);
                return R.ok(collect.get(0).getTestVal());
            } else {
                Integer max = testList.stream().map(SGRP_VAL_CHILD_DTO.Test::getTestNo).max(Integer::compareTo).orElse(0);
                List<SGRP_VAL_CHILD_DTO.Test> collect =
                        testList.stream().filter(t -> t.getTestNo().equals(max)).collect(Collectors.toList());
                return R.ok(collect.get(0).getTestVal());
            }
        }


        List<SGRP_VAL_CHILD_DTO> list = new ArrayList<>();
        subgroupDataDTOList.forEach(subgroupDataDTO -> {
            SGRP_VAL_CHILD_DTO sgrpValChildDto =
                    JSONObject.parseObject(subgroupDataDTO.getTestData(), SGRP_VAL_CHILD_DTO.class);
            if (StringUtils.isNotEmpty(testNum.get())) {
                List<SGRP_VAL_CHILD_DTO.Test> testList = sgrpValChildDto.getTestList();
                List<SGRP_VAL_CHILD_DTO.Test> collect =
                        testList.stream().filter(t -> t.getTestNo().toString().equals(testNum.get()))
                                .collect(Collectors.toList());
                sgrpValChildDto.setTestList(collect);
            }
            sgrpValChildDto.setF_SGTM(subgroupDataDTO.getF_SGTM());
            list.add(sgrpValChildDto);
        });
        /*根据子组时间排序*/
        List<SGRP_VAL_CHILD_DTO> collect =
                list.stream().sorted(Comparator.comparing(SGRP_VAL_CHILD_DTO::getF_SGTM)).collect(Collectors.toList());
        List<SGRP_VAL_CHILD_DTO.Test> testList = new ArrayList<>();
        for (SGRP_VAL_CHILD_DTO sgrpValChildDto : collect) {
            testList.addAll(sgrpValChildDto.getTestList());
        }

        if (dataValConfigDTO.getIsDisposeEndSgrp()) {
            /*将特殊处理限制在最后输入的子组*/
            testList = endValChildDto.getTestList();
        }
        List<Double> doubles = testList.stream().map(SGRP_VAL_CHILD_DTO.Test::getTestVal).collect(Collectors.toList());
        Double calculate =
                SpecialHandlingTypeEnum.calculate(SpecialHandlingTypeEnum.getType(dataValConfigDTO.getSpecialHandlingType()), doubles);
        return R.ok(calculate);

    }

    @GetMapping
    @ApiOperation("测试获取报警模型")
    public R<?> getModel() {
        ACT_GE_BYTEARRAY actGeBytearray = actGeBytearrayMapper.selectById("12a043bc-6393-11ef-9136-00ff2d0a963d");
        StringBuilder stringBuilder = new StringBuilder(2048);
        byte[] bytes = actGeBytearray.getBYTES_();
        String s = Base64.getEncoder().encodeToString(bytes);
        return R.ok();
    }

    /**
     * sql server获取所有数据库
     */
    @GetMapping("/getDatabases")
    @ApiOperation("sql server获取所有数据库")
    public R<?> getDatabases() {
        List<String> list = new ArrayList<>();
        try {
            String url = "**********************************;";
            String user = "sa";
            String pwd = "Yf@65699909.";
            DriverManager.registerDriver(new SQLServerDriver());
            Connection conn = DriverManager.getConnection(url, user, pwd);
            PreparedStatement pstmt = conn.prepareStatement("SELECT name FROM sys.databases");
            ResultSet rs = pstmt.executeQuery();

            //4.循环处理结果集中的每一条数据
            ResultSetMetaData md = rs.getMetaData();//获取键名
            int columnCount = md.getColumnCount();//获取行的数量
            while (rs.next()) { //rs.next()的作用，1. 返回是否有下一条数据， 2.将游标指向下一行
                for (int i = 1; i <= columnCount; i++) {
                    list.add(rs.getObject(i).toString());//获取键名及值
                }
            }
            //5.关闭连接对象
            rs.close();
            pstmt.close();
            conn.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.ok(list);
    }

    /**
     * sql server 指定库名后执行sql
     */
    @PostMapping("/executeSql")
    @ApiOperation("sql server 指定库名后执行sql")
    public R<?> getDatabases(String sql) {
        List<Map<String, Object>> list = new ArrayList<>();

        try {
            String url = "*************************************************************************************";
            String user = "sa";
            String pwd = "Yf@65699909.";
            DriverManager.registerDriver(new SQLServerDriver());
            Connection conn = DriverManager.getConnection(url, user, pwd);

            //2.创建执行对象
            PreparedStatement pstmt = conn.prepareStatement(sql);

            //3.执行SQL查询，返回结果集对象
            ResultSet rs = pstmt.executeQuery();

            //4.循环处理结果集中的每一条数据
            ResultSetMetaData md = rs.getMetaData();//获取键名
            int columnCount = md.getColumnCount();//获取列的数量
            boolean b = rs.next();
            while (rs.next()) { //rs.next()的作用，1. 返回是否有下一条数据， 2.将游标指向下一行
                Map<String, Object> map = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    map.put(md.getColumnName(i), rs.getObject(i));//获取键名及值
                }
                list.add(map);
            }

            //5.关闭连接对象
            rs.close();
            pstmt.close();
            conn.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.ok(list);
    }

    @ApiOperation("能力趋势任务测试")
    @GetMapping("/capabilityTrendTiming")
    public void CapabilityTrendTiming() {
        capabilityTrendTiming.calculate();

    }

    /**
     * 临时表效率测试
     */
    @ApiOperation("临时表效率测试")
    @GetMapping("/tempTableTest")
    public void tempTableTest() {
        List<Long> list = new ArrayList<>();
        for (int i = 0; i < 50000; i++) {
            list.add((long) i);
        }
        long start = System.currentTimeMillis();
        List<TEMP_TABLE_INF> arrayList = new ArrayList<>();
        String uuid = UUID.randomUUID().toString();
        list.forEach(s -> {
            TEMP_TABLE_INF tempTableInf = new TEMP_TABLE_INF();
            tempTableInf.setF_IDENTIFY(uuid);
            tempTableInf.setF_ID(s);
            arrayList.add(tempTableInf);
        });
        tempTableInfService.saveBatch(arrayList);
        long select = System.currentTimeMillis();
        log.info("插入耗时---->{}", select - start);

        tempTableInfService.remove(new LambdaQueryWrapper<>());
        long end = System.currentTimeMillis();
        log.info("总耗时---->{}", end - start);
    }

    public static void main(String[] args) {
        Path directory = Paths.get("C:\\Users\\<USER>\\Desktop\\pdf文件\\spc-文件\\csv");

        try {
            List<Path> files = getFilesByWildcard(directory, "I*.csv");

            // 根据文件的创建日期进行排序
            files.sort((path1, path2) -> {
                try {
                    FileTime creationTime1 = (FileTime) Files.getAttribute(path1, "creationTime", LinkOption.NOFOLLOW_LINKS);
                    FileTime creationTime2 = (FileTime) Files.getAttribute(path2, "creationTime", LinkOption.NOFOLLOW_LINKS);
                    return creationTime1.compareTo(creationTime2);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });

            // 输出排序后的文件列表
            for (Path file : files) {
                File file1 = file.toFile();

                System.out.println(file.getFileName() + " - Created: " + getCreationTime(file));
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /* 根据通配符获取文件夹中符合条件的文件列表*/
    private static List<Path> getFilesByWildcard(Path directory, String wildcardPattern) throws IOException {
        List<Path> files = new ArrayList<>();
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(directory, wildcardPattern)) {
            for (Path file : stream) {
                if (Files.isRegularFile(file)) {
                    files.add(file);
                }
            }
        }
        return files;
    }

    private static FileTime getCreationTime(Path path) {
        try {
            return (FileTime) Files.getAttribute(path, "creationTime", LinkOption.NOFOLLOW_LINKS);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * @param sheet   表单
     * @param cellRow 被判断的单元格的行号
     * @param cellCol 被判断的单元格的列号
     * @return row: 行数；col列数
     * @throws IOException
     * <AUTHOR>
     */
    private static Map<String, Integer> getMergerCellRegionRow(Sheet sheet, int cellRow,
                                                               int cellCol) {
        Map<String, Integer> map = new HashMap<>();
        int retVal = 0, retCol = 0;
        int sheetMergerCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergerCount; i++) {
            CellRangeAddress cra = (CellRangeAddress) sheet.getMergedRegion(i);
            int firstRow = cra.getFirstRow(); // 合并单元格CELL起始行
            int firstCol = cra.getFirstColumn(); // 合并单元格CELL起始列
            int lastRow = cra.getLastRow(); // 合并单元格CELL结束行
            int lastCol = cra.getLastColumn(); // 合并单元格CELL结束列
            if (cellRow >= firstRow && cellRow <= lastRow) { // 判断该单元格是否是在合并单元格中
                if (cellCol >= firstCol && cellCol <= lastCol) {
                    retVal = lastRow - firstRow + 1; // 得到合并的行数
                    retCol = lastCol - firstCol + 1; // 得到合并的列数
                    break;
                }
            }
        }
        map.put("row", retVal);
        map.put("col", retCol);
        return map;
    }

    private static Integer isMergedRegion(Sheet sheet, int row, int column) {
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress range = sheet.getMergedRegion(i);
            int firstColumn = range.getFirstColumn();
            int lastColumn = range.getLastColumn();
            int firstRow = range.getFirstRow();
            int lastRow = range.getLastRow();
            if (row >= firstRow && row <= lastRow) {
                if (column >= firstColumn && column <= lastColumn) {
                    return i;
                }
            }
        }
        return -1;
    }

    public static List<CellRowAndColDTO> readDiffDataBySheet(Sheet sheet, int startRows, int endRows) {
        List<CellRowAndColDTO> result = new ArrayList<>();
        for (int rowIndex = startRows, z = sheet.getLastRowNum(); rowIndex <= z; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                continue;
            }

            int rowSize = row.getLastCellNum();
            for (int columnIndex = 0; columnIndex < rowSize; columnIndex++) {
                CellRowAndColDTO dto = new CellRowAndColDTO();
                Cell cell = row.getCell(columnIndex);
                if (cell != null) {
                    // 读取单元格数据格式（标记为字符串）
                    cell.setCellType(CellType.STRING);
                    String value = cell.getStringCellValue();
                    if (StringUtils.isEmpty(value)) continue;
                    if (0 != isMergedRegion(sheet, rowIndex, columnIndex)) {//判断是否合并格
                        // 处理有值的cell
//						if (StringUtils.isEmpty(value)) {
//							continue;
//						}
                        dto.setRow(rowIndex + 1);
                        dto.setCol(columnIndex + 1);
                        Map<String, Integer> map = getMergerCellRegionRow(sheet, rowIndex, columnIndex);//获取合并的行列
                        dto.setCellCol(map.get("col") == 0 ? 1 : map.get("col"));
                        dto.setCellRow(map.get("row") == 0 ? 1 : map.get("row"));
                        dto.setCellValue(value);
                        result.add(dto);
                    } else {
                        dto.setRow(rowIndex + 1);
                        dto.setCol(columnIndex + 1);
                        Map<String, Integer> map = getMergerCellRegionRow(sheet, rowIndex, columnIndex);//获取合并的行列
                        dto.setCellCol(1);
                        dto.setCellRow(1);
                        dto.setCellValue(value);
                        result.add(dto);
                    }
                }
            }
            if (endRows == rowIndex) break;
        }
        List<CellRowAndColDTO> dtos = new ArrayList<>();
        Map<Integer, List<CellRowAndColDTO>> map = result.stream().collect(Collectors.groupingBy(CellRowAndColDTO::getRow));//根据行进行分组
        map.forEach((k, v) -> {
            for (int i = 0; i < v.size(); i++) {
                if (i != 0) {
                    Integer col = dtos.get(dtos.size() - 1).getCol() + dtos.get(dtos.size() - 1).getCellCol();
                    if (v.get(i).getCol() == col) {
                        dtos.add(v.get(i));
                        continue;
                    }
                } else {
                    dtos.add(v.get(i));
                }

            }
        });

        List<CellRowAndColDTO> dtos2 = new ArrayList<>();
        Map<Integer, List<CellRowAndColDTO>> map2 = dtos.stream().collect(Collectors.groupingBy(CellRowAndColDTO::getCol));//根据列分组
        map2.forEach((k, v) -> {
            for (int i = 0; i < v.size(); i++) {
                if (i != 0) {
                    if (v.get(i).getCellRow() != 1) {
                        if (v.get(i).getCellCol() == v.get(i - 1).getCellCol() && v.get(i).getCellRow() == v.get(i - 1).getCellRow()) {
                            if (v.get(i).getCellRow() == 1 && v.get(i).getCellCol() == 1) {
                                dtos2.add(v.get(i));
                                continue;
                            } else {
                                if (StringUtils.isBlank((v.get(i).getCellValue()))) {
                                    continue;
                                }
                            }
                        }
                    }

                }
                dtos2.add(v.get(i));
            }
        });
        return dtos2;
    }

    @ApiOperation("解析excel")
    @PostMapping("/excel")
    public R<?> excel(@RequestPart MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(1); // 获取第一个工作表
            List<CellRowAndColDTO> cellRowAndColDTOS = readDiffDataBySheet(sheet, 0, 21);
        } catch (Exception e) {

        }
        return R.ok();
    }

    /**
     * 生产mysql注释语句
     */
    @ApiOperation("生成mysql注释语句")
    @PostMapping("/mysqlNote")
    public R<?> mysqlNote(@RequestBody MysqlNoteDto mysqlNoteDto) {
        String[] split = mysqlNoteDto.getFiledSql().split(",");
        StringBuilder sb = new StringBuilder();
        sb.append("ALTER TABLE ").append(mysqlNoteDto.getTableName()).append("\r\n");
        int length = split.length;
        for (int i = 0; i < split.length; i++) {
            sb.append("MODIFY COLUMN ");
            String s = split[i];
            if (mysqlNoteDto.getBoolList().get(i)) {
                s.replace("DEFAULT NULL", "NOT NULL");
            }
            sb.append(s);
            if (StringUtils.isNotBlank(mysqlNoteDto.getNoteList().get(i))) {
                sb.append(" COMMENT ").append("'").append(mysqlNoteDto.getNoteList().get(i)).append("'");
            }
            if (i == 0) {
                sb.append(" FIRST");
            } else {
                sb.append(" AFTER ").append("'").append(mysqlNoteDto.getFiledList().get(i - 1)).append("'");
            }
            if (i == length - 1) {
                sb.append(";\r\n");
            }else {
                sb.append(",\r\n");
            }
        }
        return R.ok(sb.toString());
    }

    @Data
    class MysqlNoteDto {
        /*字段sql列表*/
        String filedSql;
        /*字段是否为null*/
        List<String> filedList;
        /*字段是否为null*/
        List<Boolean> boolList;
        /*字段注释*/
        List<String> noteList;
        /*表名*/
        String tableName;
        /*表注释*/
        String tableNote;
    }


    @ApiOperation("解析excel")
    @GetMapping("/jmData")
    public Object excel() {
        final com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
        final JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("tableName", "test");
        jsonObject1.put("tableNote", "test");
        jsonObject1.put("filedSql", "test");
        jsonObject1.put("filedList", "test");
        jsonObject1.put("boolList", "test");
        jsonObject1.put("noteList", "test");

        jsonObject.put("data", jsonObject1);
        return jsonObject;
    }
}
