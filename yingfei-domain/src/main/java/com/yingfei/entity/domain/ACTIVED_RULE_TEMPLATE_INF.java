package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存报警规则模板信息表
 * @TableName ACTIVED_RULE_TEMPLATE_INF
 */
@TableName(value ="ACTIVED_RULE_TEMPLATE_INF")
@Data
public class ACTIVED_RULE_TEMPLATE_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ARTP;

    /**
     * 分公司主键
     */
    private Long F_DIV = 0L;

    /**
     * 报警规则模板名称
     */
    private String F_NAME;

    /**
     * 图表1所激活的报警规则
     */
    private String F_CHART_ONE;

    /**
     * 图表2所激活的报警规则
     */
    private String F_CHART_TWO;

    /**
     * 图表3所激活的报警规则
     */
    private String F_CHART_THREE;

    /**
     * 报警规则模板因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    public static ACTIVED_RULE_TEMPLATE_INF init(){
        ACTIVED_RULE_TEMPLATE_INF activedRuleTemplateInf = new ACTIVED_RULE_TEMPLATE_INF();
        BeanUtils.setAllFieldsToNull(activedRuleTemplateInf);
        return activedRuleTemplateInf;
    }


}
